# MQTT连接问题诊断和修复

## 🔍 问题分析

从日志中可以看到以下问题：

1. **认证失败**：`Connection refused: Not authorized` (错误码5)
2. **多个MQTT客户端冲突**：有新的ExternalMqttService和旧的VendorMqttClientService同时运行
3. **主题名称不一致**：旧客户端使用 `vendor/*`，新客户端使用 `xstar/*`

## 🛠️ 解决方案

### 1. 更新环境变量配置

已更新 `.env` 文件，添加正确的外部MQTT配置：

```env
# 外部MQTT配置（连接到厂商MQTT服务器）
MQTT_XSTAR_HOST=**************
MQTT_XSTAR_PORT=10079
MQTT_XSTAR_USERNAME=xstar_platform
MQTT_XSTAR_PASSWORD=your_mqtt_api_key
MQTT_XSTAR_CLIENT_ID=platform_client
```

### 2. 更新厂商配置文件

已更新 `config/vendor-mqtt-config.json` 中的密码：

```json
{
  "vendors": {
    "ABC_TOYS": {
      "username": "vendor_ABC_TOYS",
      "password": "abc123456789abcdef123456789abcdef12345678",
      "clientId": "ai_backend_ABC_TOYS"
    }
  }
}
```

### 3. 禁用旧的MQTT客户端自动启动

问题是有多个MQTT客户端在同时运行：

- **新的ExternalMqttService** (TypeScript) - 使用 `xstar/*` 主题
- **旧的VendorMqttClientService** (JavaScript) - 使用 `vendor/*` 主题

## 🚀 修复步骤

### 步骤1：配置正确的API密钥

请在 `.env` 文件中设置正确的MQTT密码：

```bash
# 编辑 .env 文件
nano backend/.env

# 更新以下配置
MQTT_XSTAR_PASSWORD=your_actual_mqtt_api_key
```

### 步骤2：重启服务

```bash
# 停止当前服务
pkill -f "node.*app"

# 重新启动
cd backend
npm run dev
```

### 步骤3：验证连接

启动后应该看到类似日志：

```
外部MQTT连接成功
✅ 成功订阅主题: xstar/callback
✅ 成功订阅主题: xstar/heartbeat
✅ 成功订阅设备主题: xstar/DOLL_001/response
✅ 成功订阅设备主题: xstar/DOLL_001/heartbeat_ack
✅ 成功订阅设备主题: xstar/DOLL_001/error
📱 设备 DOLL_001 已完成主题订阅
```

## 🔧 如果仍有问题

### 检查1：确认MQTT服务器配置

```bash
# 测试MQTT连接
mosquitto_pub -h ************** -p 10079 \
  -u xstar_platform -P your_actual_mqtt_api_key \
  -t test/topic -m "test message"
```

### 检查2：确认没有旧客户端在运行

```bash
# 检查是否有其他MQTT客户端进程
ps aux | grep mqtt
ps aux | grep vendor

# 如果有，停止它们
pkill -f "vendor.*mqtt"
pkill -f "mqtt.*client"
```

### 检查3：查看详细日志

```bash
# 启动时查看详细日志
cd backend
DEBUG=mqtt* npm run dev
```

## 📡 正确的MQTT主题结构

### 发布主题（设备→服务器）
- `xstar/callback` - 用户对话输入
- `xstar/heartbeat` - 设备心跳

### 订阅主题（服务器→设备）
- `xstar/{device_id}/response` - AI回复
- `xstar/{device_id}/heartbeat_ack` - 心跳确认
- `xstar/{device_id}/error` - 错误消息

### 确认主题（设备→服务器）
- `xstar/{device_id}/response` - 设备确认收到AI回复
- `xstar/{device_id}/heartbeat_ack` - 设备确认收到心跳确认
- `xstar/{device_id}/error` - 设备确认收到错误消息

## 🎯 预期结果

修复后，您应该看到：

1. **连接成功**：无认证错误
2. **主题订阅成功**：所有 `xstar/*` 主题订阅成功
3. **设备自动订阅**：所有现有设备的响应主题自动订阅
4. **双向通信**：可以接收设备消息并发送回复

## 🆘 紧急修复

如果问题持续，可以临时禁用旧的MQTT客户端：

```bash
# 重命名旧的MQTT客户端服务文件
cd backend/src/services
mv vendorMqttClientService.js vendorMqttClientService.js.backup

# 重命名相关路由文件
cd ../routes
mv vendorMqttRoutes.js vendorMqttRoutes.js.backup

# 重启服务
cd ../..
npm run dev
```

这样只会运行新的ExternalMqttService，避免冲突。

## 📞 技术支持

如果问题仍然存在，请提供：

1. 完整的启动日志
2. MQTT服务器的认证配置
3. 网络连接测试结果

我们可以进一步诊断和解决问题。
