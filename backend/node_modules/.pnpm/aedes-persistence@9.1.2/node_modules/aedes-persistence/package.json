{"name": "aedes-persistence", "version": "9.1.2", "description": "The spec for an Aedes persistence, with abstract tests and a fast in-memory implementation.", "main": "persistence.js", "types": "types/index.d.ts", "scripts": {"lint": "standard --verbose | snazzy", "lint-fix": "standard --fix", "unit": "tape test.js | faucet", "test:typescript": "tsd", "test": "npm run lint && npm run unit && tsd", "coverage": "nyc --reporter=lcov tape test.js", "test:ci": "npm run lint && npm run coverage && npm run test:typescript", "license-checker": "license-checker --production --onlyAllow='MIT;ISC;BSD-3-Clause;BSD-2-Clause'", "release": "read -p 'GITHUB_TOKEN: ' GITHUB_TOKEN && export GITHUB_TOKEN=$GITHUB_TOKEN && release-it --disable-metrics"}, "release-it": {"github": {"release": true}, "git": {"tagName": "v${version}"}, "hooks": {"before:init": ["npm run test"]}, "npm": {"publish": true}}, "pre-commit": ["test"], "repository": {"type": "git", "url": "git+https://github.com/moscajs/aedes-persistence.git"}, "keywords": ["mqtt", "broker", "persistence", "aedes"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "Gnought", "url": "https://github.com/gnought"}], "license": "MIT", "bugs": {"url": "https://github.com/moscajs/aedes-persistence/issues"}, "homepage": "https://github.com/moscajs/aedes-persistence#readme", "engines": {"node": ">=14"}, "devDependencies": {"@types/node": "^17.0.29", "aedes": "^0.46.3", "faucet": "0.0.1", "license-checker": "^25.0.1", "mqemitter": "^4.5.0", "nyc": "^15.1.0", "pre-commit": "^1.2.2", "release-it": "^14.14.2", "snazzy": "^9.0.0", "standard": "^17.0.0", "tape": "^5.5.3", "tsd": "^0.20.0"}, "dependencies": {"aedes-packet": "^3.0.0", "qlobber": "^7.0.0"}}