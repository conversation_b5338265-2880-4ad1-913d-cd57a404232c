{
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "standard"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "env": { "node": true },
  "parserOptions": {
    "ecmaVersion": 6,
    "sourceType": "module",
    "project": "./types/tsconfig.json",
    "createDefaultProgram": true
  },
  "rules": {
    "linebreak-style": ["warn", "unix"],
    "no-console": "off",
    "semi": ["error", "never"],
    "import/export": "off", // this errors on multiple exports (overload interfaces)
    "@typescript-eslint/indent": ["error", 2]
  },
  "overrides": [
    {
      "files": ["*.d.ts", "*.test-d.ts"],
      "rules": {
        "no-dupe-class-members": "off",
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/triple-slash-reference": "off"
      }
    },
    {
      "files": ["*.test-d.ts"],
      "rules": {
        "no-unused-vars": "off",
        "node/handle-callback-err": "off",
        "@typescript-eslint/no-empty-function": "off",
        "@typescript-eslint/explicit-function-return-type": "off",
        "@typescript-eslint/no-unused-vars": "off",
        "@typescript-eslint/no-non-null-assertion": "off"
      },
      "globals": {
        "NodeJS": "readonly"
      }
    }
  ]
}
