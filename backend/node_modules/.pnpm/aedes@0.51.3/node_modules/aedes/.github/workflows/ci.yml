name: CI

on:
  push:
    paths-ignore:
      - 'docs/**'
      - '*.md'
  pull_request:
    paths-ignore:
      - 'docs/**'
      - '*.md'

permissions:
  contents: read

jobs:
  dependency-review:
    name: Dependency Review
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    steps:
        - name: Checkout repository
          uses: actions/checkout@v4
          with:
            persist-credentials: false

        - name: Dependency review
          uses: actions/dependency-review-action@v4

  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        node-version: [16, 18, 20]
        os: [ubuntu-latest, windows-latest, macOS-latest]
      fail-fast: false
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          check-latest: true
          cache: npm
          cache-dependency-path: package.json

      - name: Install
        run: |
          npm install --ignore-scripts

      - name: Check licenses
        run: |
          npm run license-checker

      - name: Run tests
        run: |
          npm run test:ci

      - name: Coveralls Parallel
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ secrets.github_token }}
          parallel: true
          flag-name: run-${{ matrix.node-version }}-${{ matrix.os }}

  coverage:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Coveralls Finished
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          parallel-finished: true
