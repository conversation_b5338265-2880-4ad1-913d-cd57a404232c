<!-- markdownlint-disable MD013 MD024 -->
# aedes-packet

![ci](https://github.com/moscajs/aedes-packet/workflows/ci/badge.svg)
[![Known Vulnerabilities](https://snyk.io/test/github/moscajs/aedes-packet/badge.svg)](https://snyk.io/test/github/moscajs/aedes-packet)
[![js-standard-style](https://img.shields.io/badge/code%20style-standard-brightgreen.svg?style=flat)](http://standardjs.com/)\
[![Dependencies Status](https://david-dm.org/moscajs/aedes-packet/status.svg)](https://david-dm.org/moscajs/aedes-packet)
[![devDependencies Status](https://david-dm.org/moscajs/aedes-packet/dev-status.svg)](https://david-dm.org/moscajs/aedes-packet?type=dev)\
[![NPM version](https://img.shields.io/npm/v/aedes-packet.svg?style=flat)](https://www.npmjs.com/package/aedes-packet)
[![NPM downloads](https://img.shields.io/npm/dm/aedes-packet.svg?style=flat)](https://www.npmjs.com/package/aedes-packet)

Basic data structure for packets in [Aedes](https://www.npmjs.com/aedes), packaged up for perf and reusability between modules.

See [./packet.js](./packet.js) and [./test.js](./test.js) for
documentation and usage.

## Collaborators

* [__Gnought__](https://github.com/gnought)

## License

MIT
