{"name": "aedes-packet", "version": "3.0.0", "description": "Basic data structure for packets in Aedes ", "main": "packet.js", "types": "packet.d.ts", "scripts": {"lint": "npm run lint:standard && npm run lint:typescript && npm run lint:markdown", "lint:standard": "standard --verbose | snazzy", "lint:typescript": "standard --parser @typescript-eslint/parser --plugin @typescript-eslint/eslint-plugin test/types/*.ts packet.d.ts", "lint:markdown": "markdownlint README.md", "unit": "tap test.js", "typescript": "tsc --project ./test/types/tsconfig.json", "test": "npm run lint && npm run unit && npm run typescript", "test:ci": "npm run test", "license-checker": "license-checker --production --onlyAllow='MIT;ISC;BSD-3-Clause;BSD-2-Clause'", "release": "read -p 'GITHUB_TOKEN: ' GITHUB_TOKEN && export GITHUB_TOKEN=$GITHUB_TOKEN && release-it --disable-metrics"}, "release-it": {"github": {"release": true}, "git": {"tagName": "v${version}"}, "hooks": {"before:init": ["npm run test"]}, "npm": {"publish": true}}, "pre-commit": ["test"], "repository": {"type": "git", "url": "git+https://github.com/moscajs/aedes-packet.git"}, "keywords": ["mqtt", "packet", "broker", "aedes"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "Gnought", "url": "https://github.com/gnought"}], "license": "MIT", "bugs": {"url": "https://github.com/moscajs/aedes-packet/issues"}, "homepage": "https://github.com/moscajs/aedes-packet#readme", "engines": {"node": ">=14"}, "dependencies": {"mqtt-packet": "^7.0.0"}, "devDependencies": {"@types/node": "^17.0.0", "@typescript-eslint/eslint-plugin": "^4.17.0", "@typescript-eslint/parser": "^4.17.0", "faucet": "0.0.1", "license-checker": "^25.0.1", "markdownlint-cli": "^0.29.0", "pre-commit": "^1.2.2", "release-it": "^15.0.0", "snazzy": "^9.0.0", "standard": "^16.0.3", "tap": "^15.2.3", "typescript": "^4.6.4"}}