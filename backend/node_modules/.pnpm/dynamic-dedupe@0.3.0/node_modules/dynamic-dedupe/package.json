{"name": "dynamic-dedupe", "version": "0.3.0", "description": "Dedupes node modules as they are being required  which works even when dependencies are linked via ln -s or npm link.", "main": "index.js", "scripts": {"test-main": "tap test/*.js", "test-0.8": "nave use 0.8 npm run test-main", "test-0.10": "nave use 0.10 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10", "test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi"}, "repository": {"type": "git", "url": "git://github.com/thlorenz/dynamic-dedupe.git"}, "homepage": "https://github.com/thlorenz/dynamic-dedupe", "dependencies": {"xtend": "^4.0.0"}, "devDependencies": {"nave": "~0.4.3", "tap": "~0.4.3"}, "keywords": ["dedupe", "npm", "require", "extension", "link"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://thlorenz.com"}, "license": {"type": "MIT", "url": "https://github.com/thlorenz/dynamic-dedupe/blob/master/LICENSE"}, "engine": {"node": ">=0.6"}}