{"name": "commist", "version": "3.2.0", "description": "Build your commands on minimist!", "main": "index.js", "scripts": {"test": "standard && tape test.js"}, "pre-commit": "test", "repository": {"type": "git", "url": "https://github.com/mcollina/commist.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/mcollina/commist/issues"}, "homepage": "https://github.com/mcollina/commist", "dependencies": {}, "devDependencies": {"minimist": "^1.1.0", "pre-commit": "^1.0.0", "standard": "^17.0.0", "tape": "^5.0.0"}}