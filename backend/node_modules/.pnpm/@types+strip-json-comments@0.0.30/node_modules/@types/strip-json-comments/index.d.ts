// Type definitions for strip-json-comments
// Project: https://github.com/sindresorhus/strip-json-comments
// Definitions by: <PERSON> <PERSON> <https://github.com/dmoonfire>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped


interface StripJsonOptions {
    whitespace?: boolean;
}

declare function stripJsonComments(input: string, opts?: StripJsonOptions): string;
declare namespace stripJsonComments { }
export = stripJsonComments;
