# Docker部署指南

## 概述

本指南介绍如何使用Docker和Docker Compose部署多用户AI对话平台。Docker部署方式提供了完整的容器化解决方案，包括应用服务、MySQL数据库、Redis缓存和Nginx反向代理。

## 系统要求

- Docker 20.0+
- Docker Compose 2.0+
- 至少4GB内存
- 至少10GB磁盘空间

## 快速部署

### 1. 准备环境

```bash
# 检查Docker版本
docker --version
docker-compose --version

# 克隆项目
git clone <your-repo>
cd backend
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量（重要！）
vim .env
```

**必须配置的环境变量：**
```env
# 阿里云API密钥（必须）
ALIYUN_API_KEY=your_aliyun_dashscope_api_key

# JWT密钥（生产环境必须修改）
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production

# MQTT配置（已更新为新地址）
MQTT_VENDOR_HOST=**************
MQTT_VENDOR_PORT=10079
MQTT_VENDOR_USERNAME=vendor_platform
MQTT_VENDOR_PASSWORD=your_mqtt_api_key
```

### 3. 运行系统检查

```bash
# 安装检查脚本依赖
npm install

# 运行系统检查
node system-check.js
```

### 4. 部署服务

```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 构建镜像
./deploy.sh build

# 启动所有服务
./deploy.sh start
```

### 5. 验证部署

```bash
# 查看服务状态
docker-compose ps

# 查看日志
./deploy.sh logs

# 测试API
curl http://localhost:3000/health
```

## 服务架构

### 容器服务

| 服务名 | 端口 | 描述 |
|--------|------|------|
| ai-platform | 3000 | 主应用服务 |
| mysql | 3306 | MySQL数据库 |
| redis | 6379 | Redis缓存 |
| nginx | 80/443 | 反向代理 |

### 网络架构

```
Internet → Nginx (80/443) → AI Platform (3000)
                          ↓
                     MySQL (3306) + Redis (6379)
                          ↓
                     MQTT (**************:10079)
```

## 部署脚本使用

### 基本操作

```bash
# 构建镜像
./deploy.sh build

# 启动服务
./deploy.sh start

# 停止服务
./deploy.sh stop

# 重启服务
./deploy.sh restart

# 查看所有日志
./deploy.sh logs

# 查看特定服务日志
./deploy.sh logs ai-platform
./deploy.sh logs mysql
./deploy.sh logs redis
```

### 维护操作

```bash
# 运行系统检查
./deploy.sh check

# 初始化数据库
./deploy.sh init-db

# 备份数据
./deploy.sh backup

# 清理所有资源（危险操作）
./deploy.sh clean
```

## 配置说明

### Docker Compose配置

主要配置文件：`docker-compose.yml`

**环境变量覆盖：**
- 生产环境建议使用外部环境变量
- 敏感信息不要写在docker-compose.yml中

**数据持久化：**
- MySQL数据：`mysql_data` volume
- Redis数据：`redis_data` volume
- 应用日志：`./logs` 目录挂载

### Nginx配置

配置文件：`nginx.conf`

**功能特性：**
- 反向代理到应用服务
- Gzip压缩
- 安全头设置
- Socket.IO支持
- 健康检查代理

**SSL配置（可选）：**
```bash
# 创建SSL目录
mkdir ssl

# 放置证书文件
cp your-cert.pem ssl/cert.pem
cp your-key.pem ssl/key.pem

# 取消注释nginx.conf中的HTTPS配置
```

## 监控和日志

### 健康检查

```bash
# 应用健康检查
curl http://localhost:3000/health

# 数据库健康检查
docker-compose exec mysql mysqladmin ping -h localhost -u root -proot_password_2024

# Redis健康检查
docker-compose exec redis redis-cli ping
```

### 日志管理

```bash
# 查看实时日志
./deploy.sh logs -f

# 查看特定时间段日志
docker-compose logs --since="2024-01-01T00:00:00" --until="2024-01-02T00:00:00"

# 导出日志
docker-compose logs > application.log
```

### 性能监控

```bash
# 查看容器资源使用
docker stats

# 查看容器详细信息
docker-compose exec ai-platform top
```

## 数据管理

### 数据库管理

```bash
# 连接数据库
docker-compose exec mysql mysql -u root -proot_password_2024 aidoll

# 备份数据库
docker-compose exec mysql mysqldump -u root -proot_password_2024 aidoll > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u root -proot_password_2024 aidoll < backup.sql
```

### Redis管理

```bash
# 连接Redis
docker-compose exec redis redis-cli

# 查看Redis信息
docker-compose exec redis redis-cli info

# 备份Redis
docker-compose exec redis redis-cli --rdb backup.rdb
```

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 查看详细日志
   ./deploy.sh logs
   
   # 检查端口占用
   netstat -tulpn | grep :3000
   
   # 重新构建镜像
   ./deploy.sh build
   ```

2. **数据库连接失败**
   ```bash
   # 检查MySQL容器状态
   docker-compose ps mysql
   
   # 查看MySQL日志
   ./deploy.sh logs mysql
   
   # 重启MySQL
   docker-compose restart mysql
   ```

3. **MQTT连接失败**
   ```bash
   # 检查网络连接
   telnet ************** 10079
   
   # 查看应用日志中的MQTT相关信息
   ./deploy.sh logs ai-platform | grep MQTT
   ```

4. **内存不足**
   ```bash
   # 查看系统资源
   free -h
   df -h
   
   # 清理Docker资源
   docker system prune -f
   ```

### 调试技巧

1. **进入容器调试**
   ```bash
   # 进入应用容器
   docker-compose exec ai-platform sh
   
   # 进入数据库容器
   docker-compose exec mysql bash
   ```

2. **查看容器网络**
   ```bash
   # 查看网络配置
   docker network ls
   docker network inspect backend_ai-platform-network
   ```

3. **测试服务连通性**
   ```bash
   # 从应用容器测试数据库连接
   docker-compose exec ai-platform nc -zv mysql 3306
   
   # 从应用容器测试Redis连接
   docker-compose exec ai-platform nc -zv redis 6379
   ```

## 生产环境部署

### 安全配置

1. **修改默认密码**
   ```env
   MYSQL_ROOT_PASSWORD=your_secure_password
   MYSQL_PASSWORD=your_secure_password
   REDIS_PASSWORD=your_secure_password
   JWT_SECRET=your_secure_jwt_secret
   ```

2. **网络安全**
   - 使用防火墙限制端口访问
   - 配置SSL证书
   - 使用VPN或内网部署

3. **数据备份**
   ```bash
   # 设置定时备份
   crontab -e
   # 添加：0 2 * * * /path/to/deploy.sh backup
   ```

### 性能优化

1. **资源限制**
   ```yaml
   # 在docker-compose.yml中添加
   deploy:
     resources:
       limits:
         memory: 2G
         cpus: '1.0'
   ```

2. **数据库优化**
   ```sql
   # MySQL配置优化
   SET GLOBAL innodb_buffer_pool_size = 1073741824;
   SET GLOBAL max_connections = 200;
   ```

3. **Redis优化**
   ```bash
   # Redis内存优化
   docker-compose exec redis redis-cli config set maxmemory 512mb
   docker-compose exec redis redis-cli config set maxmemory-policy allkeys-lru
   ```

## 更新和维护

### 应用更新

```bash
# 1. 备份数据
./deploy.sh backup

# 2. 停止服务
./deploy.sh stop

# 3. 更新代码
git pull

# 4. 重新构建
./deploy.sh build

# 5. 启动服务
./deploy.sh start
```

### 数据库迁移

```bash
# 如果有数据库结构变更
docker-compose exec mysql mysql -u root -proot_password_2024 aidoll < migration.sql
```

## 总结

Docker部署方式提供了：
- ✅ 完整的容器化解决方案
- ✅ 一键部署和管理
- ✅ 数据持久化和备份
- ✅ 负载均衡和反向代理
- ✅ 健康检查和监控
- ✅ 生产环境就绪

通过本指南，您可以快速在服务器上部署多用户AI对话平台，并进行有效的运维管理。
