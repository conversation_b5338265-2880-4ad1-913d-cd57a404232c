# MQTT字段更新总结 - vendor → xstar

## 🔄 更新概述

已成功将MQTT相关代码中的所有 `vendor` 字段更换为 `xstar`，以符合新的命名规范。

## ✅ 已更新的文件

### 1. 核心服务文件

#### `src/services/externalMqttService.ts` ✅
**更新内容：**
- 接口名称：`VendorCallbackMessage` → `XstarCallbackMessage`
- 接口名称：`VendorHeartbeatMessage` → `XstarHeartbeatMessage`
- 字段名称：`vendor_id` → `xstar_id`
- 方法名称：`validateVendorAccess` → `validateXstarAccess`
- MQTT主题：
  - `vendor/callback` → `xstar/callback`
  - `vendor/heartbeat` → `xstar/heartbeat`
  - `vendor/{device_id}/response` → `xstar/{device_id}/response`
  - `vendor/{device_id}/heartbeat_ack` → `xstar/{device_id}/heartbeat_ack`
  - `vendor/{device_id}/error` → `xstar/{device_id}/error`

#### `src/controllers/deviceVendor.ts` ✅
**更新内容：**
- 请求参数：`vendor_id` → `xstar_id`
- 数据库字段：`vendor_id` → `xstar_id`
- 响应数据：`vendor_id` → `xstar_id`
- 统计信息：`vendor_info` → `xstar_info`

### 2. 配置文件

#### `.env.example` ✅
**更新内容：**
```env
# 修改前
MQTT_VENDOR_HOST=**************
MQTT_VENDOR_PORT=10079
MQTT_VENDOR_USERNAME=vendor_platform
MQTT_VENDOR_PASSWORD=your_mqtt_api_key
MQTT_VENDOR_CLIENT_ID=platform_client

# 修改后
MQTT_XSTAR_HOST=**************
MQTT_XSTAR_PORT=10079
MQTT_XSTAR_USERNAME=xstar_platform
MQTT_XSTAR_PASSWORD=your_mqtt_api_key
MQTT_XSTAR_CLIENT_ID=platform_client
```

#### `docker-compose.yml` ✅
**更新内容：**
- 环境变量名称全部从 `MQTT_VENDOR_*` 更新为 `MQTT_XSTAR_*`

### 3. 数据库相关

#### `src/config/init.sql` ✅
**更新内容：**
- 表字段：`vendor_id` → `xstar_id`
- 索引名称：`idx_vendor_id` → `idx_xstar_id`
- 测试数据中的字段名称更新

#### `migrate-vendor-to-xstar.sql` ✅ (新增)
**功能：**
- 数据库迁移脚本
- 安全地将现有数据库的字段名从 `vendor_id` 更新为 `xstar_id`
- 包含数据验证和回滚保护

### 4. 文档更新

#### `MQTT对接实现文档.md` ✅
**更新内容：**
- 所有MQTT主题名称
- 环境变量配置示例
- 数据库表结构
- API示例代码

## 📡 新的MQTT主题结构

### 发布主题（设备→服务器）
- `xstar/callback` - 对话回调
- `xstar/heartbeat` - 设备心跳

### 订阅主题（服务器→设备）
- `xstar/{device_id}/response` - AI回复
- `xstar/{device_id}/heartbeat_ack` - 心跳确认
- `xstar/{device_id}/error` - 错误消息

## 🔧 环境变量更新

### 新的环境变量名称
```env
MQTT_XSTAR_HOST=**************
MQTT_XSTAR_PORT=10079
MQTT_XSTAR_USERNAME=xstar_platform
MQTT_XSTAR_PASSWORD=your_mqtt_api_key
MQTT_XSTAR_CLIENT_ID=platform_client
```

## 🗄️ 数据库更新

### 表结构变更
```sql
-- device_vendors 表
CREATE TABLE device_vendors (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  xstar_id VARCHAR(50) UNIQUE NOT NULL,  -- 原 vendor_id
  name VARCHAR(100) NOT NULL,
  description TEXT,
  contact_info JSON,
  api_key VARCHAR(64) NOT NULL,
  user_id BIGINT NOT NULL,
  tenant_id BIGINT,
  status ENUM('active', 'inactive', 'suspended'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 索引更新
- `idx_vendor_id` → `idx_xstar_id`

## 🚀 部署更新步骤

### 1. 更新环境变量
```bash
# 编辑 .env 文件
cp .env.example .env
# 更新所有 MQTT_VENDOR_* 为 MQTT_XSTAR_*
```

### 2. 数据库迁移（如果已有数据）
```bash
# 备份数据库
mysqldump -u root -p aidoll > backup_before_migration.sql

# 执行迁移脚本
mysql -u root -p aidoll < migrate-vendor-to-xstar.sql
```

### 3. 重新部署服务
```bash
# Docker部署
./deploy.sh build
./deploy.sh restart

# 或本地部署
npm run build
npm restart
```

### 4. 验证更新
```bash
# 检查MQTT连接状态
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:3000/api/external-mqtt/status

# 检查厂商列表
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:3000/api/device-vendors
```

## 📋 API接口更新

### 厂商管理接口
```bash
# 创建厂商（字段名已更新）
POST /api/device-vendors
{
  "xstar_id": "MY_COMPANY",     # 原 vendor_id
  "name": "我的公司",
  "description": "公司描述"
}

# 响应数据（字段名已更新）
{
  "code": 0,
  "data": {
    "id": 1,
    "xstar_id": "MY_COMPANY",   # 原 vendor_id
    "name": "我的公司",
    "api_key": "generated_key"
  }
}
```

## ⚠️ 注意事项

### 1. 向后兼容性
- 此更新不向后兼容
- 需要同时更新客户端代码中的字段名称
- MQTT主题名称已完全更改

### 2. 数据迁移
- 提供了安全的数据库迁移脚本
- 建议在生产环境执行前先在测试环境验证
- 执行迁移前务必备份数据库

### 3. 客户端更新
- 设备端代码需要更新MQTT主题名称
- API调用需要更新字段名称
- 环境变量配置需要更新

## ✅ 验证清单

- [ ] 环境变量已更新
- [ ] 数据库迁移已完成
- [ ] 服务重新部署成功
- [ ] MQTT连接状态正常
- [ ] 厂商管理功能正常
- [ ] 设备通信功能正常
- [ ] 文档已更新

## 🎉 更新完成

所有 `vendor` 字段已成功更换为 `xstar`：
- ✅ 代码文件已更新
- ✅ 配置文件已更新
- ✅ 数据库结构已更新
- ✅ 文档已更新
- ✅ 迁移脚本已提供

现在系统使用新的 `xstar` 命名规范，所有MQTT通信和API接口都已更新。
