const { Sequelize, DataTypes } = require('sequelize');
const dotenv = require('dotenv');

dotenv.config();

// 创建Sequelize实例
const sequelize = new Sequelize(
  process.env.DB_NAME || 'aidoll',
  process.env.DB_USER || 'aidoll',
  process.env.DB_PASSWORD || 'k3CFdxWJaLS7HHJa',
  {
    host: process.env.DB_HOST || '**************',
    dialect: 'mysql',
    timezone: '+08:00',
    define: {
      timestamps: true,
      underscored: true,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    }
  }
);

// 模型类型枚举
const ModelType = {
  LOCAL: 'local',
  CLOUD: 'cloud'
};

async function createModelsTable() {
  try {
    // 定义模型表
    await sequelize.getQueryInterface().createTable('models', {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true
      },
      model_key: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        comment: '模型唯一标识'
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '模型名称'
      },
      type: {
        type: DataTypes.ENUM(...Object.values(ModelType)),
        allowNull: false,
        comment: '模型类型'
      },
      config: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: '模型配置'
      },
      status: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 1,
        comment: '状态(1:启用,0:禁用)'
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    }, {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      comment: '模型配置表'
    });

    // 添加索引
    await sequelize.getQueryInterface().addIndex('models', ['model_key'], { name: 'idx_model_key' });
    await sequelize.getQueryInterface().addIndex('models', ['type'], { name: 'idx_type' });
    await sequelize.getQueryInterface().addIndex('models', ['status'], { name: 'idx_status' });

    // 插入初始模型数据
    await sequelize.getQueryInterface().bulkInsert('models', [
      {
        model_key: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        type: 'cloud',
        config: JSON.stringify({
          api_url: 'https://api.openai.com/v1/chat/completions',
          max_tokens: 2048,
          temperature: 0.7,
          provider: 'openai'
        }),
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        model_key: 'gpt-4',
        name: 'GPT-4',
        type: 'cloud',
        config: JSON.stringify({
          api_url: 'https://api.openai.com/v1/chat/completions',
          max_tokens: 4096,
          temperature: 0.7,
          provider: 'openai'
        }),
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        model_key: 'claude',
        name: 'Claude 3 Opus',
        type: 'cloud',
        config: JSON.stringify({
          api_url: 'https://api.anthropic.com/v1/messages',
          max_tokens: 4096,
          temperature: 0.7,
          provider: 'anthropic'
        }),
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        model_key: 'qianwen',
        name: '通义千问',
        type: 'cloud',
        config: JSON.stringify({
          api_url: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
          max_tokens: 2048,
          temperature: 0.7,
          provider: 'aliyun'
        }),
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        model_key: 'chatglm',
        name: 'ChatGLM-3',
        type: 'local',
        config: JSON.stringify({
          model_path: '/models/chatglm3-6b',
          max_tokens: 2048,
          temperature: 0.7,
          provider: 'zhipu'
        }),
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    console.log('模型表创建成功并插入初始数据');
  } catch (error) {
    console.error('创建模型表失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 执行迁移
createModelsTable(); 
