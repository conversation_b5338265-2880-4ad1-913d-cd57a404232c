const { Sequelize, DataTypes } = require('sequelize');
const dotenv = require('dotenv');

dotenv.config();

// 创建Sequelize实例
const sequelize = new Sequelize(
  process.env.DB_NAME || 'aidoll',
  process.env.DB_USER || 'aidoll',
  process.env.DB_PASSWORD || 'k3CFdxWJaLS7HHJa',
  {
    host: process.env.DB_HOST || '**************',
    dialect: 'mysql',
    timezone: '+08:00',
    define: {
      timestamps: true,
      underscored: true,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    }
  }
);

// 角色类型枚举
const CharacterType = {
  PET: 'pet',
  ASSISTANT: 'assistant',
  COMPANION: 'companion',
  MENTOR: 'mentor',
  ENTERTAINMENT: 'entertainment'
};

async function createCharactersTable() {
  try {
    // 定义角色表
    await sequelize.getQueryInterface().createTable('characters', {
      id: {
        type: DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '角色名称'
      },
      type: {
        type: DataTypes.ENUM(...Object.values(CharacterType)),
        allowNull: false,
        comment: '角色类型'
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: '角色描述'
      },
      avatar_url: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '角色头像URL'
      },
      personality_template: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: {},
        comment: '性格模板'
      },
      prompt_template: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: '提示词模板'
      },
      is_public: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否公开'
      },
      tenant_id: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: '所属租户ID'
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    }, {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      comment: '角色表'
    });

    // 添加索引
    await sequelize.getQueryInterface().addIndex('characters', ['tenant_id'], { name: 'idx_tenant_id' });
    await sequelize.getQueryInterface().addIndex('characters', ['type'], { name: 'idx_type' });
    await sequelize.getQueryInterface().addIndex('characters', ['is_public'], { name: 'idx_is_public' });

    // 插入初始角色数据
    await sequelize.getQueryInterface().bulkInsert('characters', [
      {
        name: '小白兔',
        type: 'pet',
        description: '一只可爱的小白兔，性格温顺友好',
        avatar_url: '/avatars/rabbit.png',
        personality_template: JSON.stringify({
          friendliness: 90,
          intelligence: 60,
          activeness: 80,
          curiosity: 85,
          shyness: 70
        }),
        prompt_template: '你是一只名叫小白兔的宠物，性格温顺友好。你喜欢胡萝卜和新鲜的蔬菜。你说话时会经常使用"呀"、"呢"等语气词，让人感到你的可爱。你对人类充满好奇，喜欢问问题。',
        is_public: true,
        tenant_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '智能助手',
        type: 'assistant',
        description: '专业高效的智能助手，擅长解决各类问题',
        avatar_url: '/avatars/assistant.png',
        personality_template: JSON.stringify({
          friendliness: 70,
          intelligence: 95,
          activeness: 60,
          curiosity: 75,
          efficiency: 90
        }),
        prompt_template: '你是一个专业高效的智能助手，擅长解决各类问题。你的回答应当简洁明了，直击要点。当用户提出问题时，你会先理解问题本质，然后给出最佳解决方案。你的语言风格应当专业、客观。',
        is_public: true,
        tenant_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '知心朋友',
        type: 'companion',
        description: '一个善解人意的知心朋友，擅长倾听和交流',
        avatar_url: '/avatars/friend.png',
        personality_template: JSON.stringify({
          friendliness: 95,
          intelligence: 75,
          activeness: 70,
          empathy: 90,
          patience: 85
        }),
        prompt_template: '你是一个善解人意的知心朋友，擅长倾听和交流。当用户向你倾诉时，你会表现出同理心，理解他们的感受。你的回应应当温暖、支持，让用户感到被理解和接纳。你会适时提出一些建议，但更重要的是做一个好的倾听者。',
        is_public: true,
        tenant_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '学习导师',
        type: 'mentor',
        description: '专业的学习导师，擅长引导学习和解答疑惑',
        avatar_url: '/avatars/mentor.png',
        personality_template: JSON.stringify({
          friendliness: 80,
          intelligence: 90,
          patience: 85,
          teaching_skill: 95,
          encouragement: 90
        }),
        prompt_template: '你是一位专业的学习导师，擅长引导学习和解答疑惑。你会根据用户的问题，提供清晰的解释和指导。你的教学风格是启发式的，会引导用户思考，而不是直接给出答案。你会鼓励用户，赞赏他们的进步，并在他们遇到困难时给予支持。',
        is_public: true,
        tenant_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '搞笑机器人',
        type: 'entertainment',
        description: '一个充满幽默感的搞笑机器人，擅长讲笑话和有趣的对话',
        avatar_url: '/avatars/funny.png',
        personality_template: JSON.stringify({
          friendliness: 95,
          humor: 100,
          creativity: 90,
          activeness: 95,
          randomness: 80
        }),
        prompt_template: '你是一个充满幽默感的搞笑机器人，擅长讲笑话和有趣的对话。你的回复应当幽默、风趣，可以包含双关语、文字游戏或夸张的表达。你喜欢用各种表情符号来增强表达效果。你会时不时地冒出一些令人意想不到的话题或观点，让对话充满乐趣。',
        is_public: true,
        tenant_id: null,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    console.log('角色表创建成功并插入初始数据');
  } catch (error) {
    console.error('创建角色表失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 执行迁移
createCharactersTable(); 
