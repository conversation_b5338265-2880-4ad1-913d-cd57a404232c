# MQTT对接实现文档

## 概述

本文档描述了AI娃娃平台与外部厂商设备通过MQTT协议进行对接的完整实现方案。平台作为MQTT客户端连接到厂商的MQTT服务器，接收设备消息并返回AI回复和硬件控制指令。

## 已实现功能

### 1. 外部MQTT服务集成 ✅
- 连接到厂商MQTT服务器 (mqtt.ai-doll.cn:1884)
- 自动重连机制和错误处理
- 支持多厂商设备接入
- 环境变量配置支持

### 2. 消息处理系统 ✅
- **对话回调处理** (`xstar/callback`)
  - 接收用户输入
  - 调用AI对话服务
  - 集成长期记忆系统
- **设备心跳处理** (`xstar/heartbeat`)
  - 设备状态监控
  - 电量和WiFi信号记录
- **AI回复发送** (`xstar/{device_id}/response`)
  - 智能回复生成
  - 硬件控制指令
- **错误消息处理** (`xstar/{device_id}/error`)

### 3. 硬件控制集成 ✅
- **屏幕控制**
  - 基于情感的图片显示
  - 动画播放控制
- **舵机控制**
  - 左耳、右耳、尾巴动作
  - 情感表达动作序列

### 4. 厂商管理系统 ✅
- 厂商注册和API密钥管理
- 权限控制和数据隔离
- 厂商统计和监控
- 设备关联管理

### 5. 设备状态管理 ✅
- 实时设备状态更新
- 心跳监控和离线检测
- 设备硬件信息管理
- 电量和信号强度记录

## 技术实现

### 核心文件结构

```
backend/src/
├── services/
│   └── externalMqttService.ts     # 外部MQTT服务
├── controllers/
│   ├── externalMqtt.ts           # MQTT管理控制器
│   └── deviceVendor.ts           # 厂商管理控制器
├── routes/
│   ├── externalMqtt.ts           # MQTT管理路由
│   └── deviceVendor.ts           # 厂商管理路由
└── middleware/
    └── permission.ts             # 权限控制中间件
```

### 数据库设计

#### 厂商表 (device_vendors)
```sql
CREATE TABLE device_vendors (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  xstar_id VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  contact_info JSON,
  api_key VARCHAR(64) NOT NULL,
  user_id BIGINT NOT NULL,
  tenant_id BIGINT,
  status ENUM('active', 'inactive', 'suspended'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 设备表扩展
- 添加厂商关联
- 心跳信息存储
- 硬件状态记录

## API接口

### 1. MQTT管理接口

#### 获取MQTT连接状态
```
GET /api/external-mqtt/status
Authorization: Bearer <token>
```

#### 获取设备MQTT日志
```
GET /api/external-mqtt/devices/{device_id}/logs
Authorization: Bearer <token>
```

#### 获取设备硬件状态
```
GET /api/external-mqtt/devices/{device_id}/hardware
Authorization: Bearer <token>
```

#### 发送测试消息
```
POST /api/external-mqtt/test-message
Authorization: Bearer <token>
Content-Type: application/json

{
  "device_id": "DOLL_001",
  "message_type": "response",
  "content": "测试消息内容"
}
```

### 2. 厂商管理接口

#### 获取厂商列表
```
GET /api/device-vendors
Authorization: Bearer <token>
```

#### 创建厂商
```
POST /api/device-vendors
Authorization: Bearer <token>
Content-Type: application/json

{
  "xstar_id": "ABC_TOYS",
  "name": "ABC玩具公司",
  "description": "专业的智能玩具制造商",
  "contact_info": {
    "email": "<EMAIL>",
    "phone": "+86-************"
  }
}
```

#### 重新生成API密钥
```
POST /api/device-vendors/{id}/regenerate-key
Authorization: Bearer <token>
```

## MQTT消息流程

### 1. 对话流程

```mermaid
sequenceDiagram
    participant Device as 娃娃设备
    participant MQTT as MQTT服务器
    participant Platform as AI平台
    participant AI as AI服务

    Device->>MQTT: 发布对话消息 (xstar/callback)
    MQTT->>Platform: 转发消息
    Platform->>Platform: 验证设备和厂商
    Platform->>AI: 调用AI对话服务
    AI->>Platform: 返回AI回复
    Platform->>Platform: 生成硬件控制指令
    Platform->>MQTT: 发布回复 (xstar/{device_id}/response)
    MQTT->>Device: 转发AI回复和控制指令
```

### 2. 心跳流程

```mermaid
sequenceDiagram
    participant Device as 娃娃设备
    participant MQTT as MQTT服务器
    participant Platform as AI平台

    Device->>MQTT: 发布心跳 (xstar/heartbeat)
    MQTT->>Platform: 转发心跳
    Platform->>Platform: 更新设备状态
    Platform->>MQTT: 发布心跳确认 (xstar/{device_id}/heartbeat_ack)
    MQTT->>Device: 转发确认
```

## 配置说明

### 环境变量配置

```env
# 外部MQTT配置
MQTT_XSTAR_HOST=**************
MQTT_XSTAR_PORT=10079
MQTT_XSTAR_USERNAME=xstar_platform
MQTT_XSTAR_PASSWORD=your_mqtt_api_key
MQTT_XSTAR_CLIENT_ID=platform_client
```

### 硬件控制配置

#### 情感映射表
```javascript
const emotionImages = {
  happy: 'https://assets.ai-doll.cn/emotions/happy.gif',
  sad: 'https://assets.ai-doll.cn/emotions/sad.gif',
  angry: 'https://assets.ai-doll.cn/emotions/angry.gif',
  excited: 'https://assets.ai-doll.cn/emotions/excited.gif',
  calm: 'https://assets.ai-doll.cn/emotions/calm.gif',
  surprised: 'https://assets.ai-doll.cn/emotions/surprised.gif',
  neutral: 'https://assets.ai-doll.cn/emotions/neutral.gif'
}
```

#### 舵机动作配置
```javascript
const emotionServos = {
  happy: {
    left_ear: [1, 0, 1, 0],
    right_ear: [1, 0, 1, 0],
    tail: [1, 1, 1, 1]
  },
  sad: {
    left_ear: [-1, 0, -1, 0],
    right_ear: [-1, 0, -1, 0],
    tail: [0, 0, 0, 0]
  }
}
```

## 部署和启动

### 1. 安装依赖
```bash
npm install mqtt
```

### 2. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置MQTT连接信息
```

### 3. 启动服务
```bash
npm run dev
```

### 4. 验证连接
- 查看控制台日志确认MQTT连接成功
- 使用API接口检查连接状态

## 监控和调试

### 1. 日志监控
- MQTT连接状态日志
- 消息处理日志
- 错误和异常日志

### 2. API监控
- 获取MQTT连接状态
- 查看设备活动日志
- 监控厂商统计信息

### 3. 调试工具
- 测试消息发送
- 模拟设备消息
- 硬件控制测试

## 安全考虑

### 1. 认证和授权
- API密钥管理
- 厂商权限控制
- 设备访问验证

### 2. 数据安全
- 敏感信息加密
- 传输安全保护
- 访问日志记录

### 3. 错误处理
- 连接失败重试
- 消息处理异常
- 设备离线检测

## 扩展功能

### 1. 多厂商支持
- 厂商注册管理
- 独立权限控制
- 数据隔离保护

### 2. 高级硬件控制
- 自定义动作序列
- 复杂情感表达
- 硬件状态反馈

### 3. 性能优化
- 消息队列处理
- 连接池管理
- 缓存策略优化

## 故障排除

### 常见问题

1. **MQTT连接失败**
   - 检查网络连接
   - 验证认证信息
   - 确认服务器地址和端口

2. **消息处理失败**
   - 检查设备ID有效性
   - 验证厂商权限
   - 查看错误日志

3. **硬件控制无响应**
   - 确认设备在线状态
   - 检查控制指令格式
   - 验证设备兼容性

### 调试步骤

1. 检查MQTT连接状态
2. 查看设备活动日志
3. 测试消息发送功能
4. 验证权限配置
5. 检查硬件控制指令

## 总结

本MQTT对接方案成功实现了：
- 完整的外部MQTT服务集成
- 智能对话和硬件控制
- 多厂商设备管理
- 权限控制和数据安全
- 监控和调试功能

系统已经可以投入使用，支持厂商设备通过MQTT协议与AI平台进行实时对话交互。
