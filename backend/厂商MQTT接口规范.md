# MQTT接口文档

## 连接信息

| 项目       | 值                |
| ---------- | ----------------- |
| 服务器地址 | http://ba7man.top |
| 端口       | `1884`            |
| 协议版本   | MQTT 3.1.1        |
| 认证方式   | Username/Password |

### 连接参数

```json
{
  "host": "mqtt.ai-doll.cn",
  "port": 1884,
  "username": "vendor_{VENDOR_ID}",
  "password": "{API_KEY}",
  "clientId": "vendor_device_{DEVICE_ID}",
  "clean": true,
  "keepalive": 60
}
```

## 主题规范

### 发布主题（设备→服务器）

- `vendor/callback` - 对话回调
- `vendor/heartbeat` - 设备心跳

### 订阅主题（服务器→设备）

- `vendor/{device_id}/response` - AI回复
- `vendor/{device_id}/heartbeat_ack` - 心跳确认
- `vendor/{device_id}/error` - 错误消息

## 对话回调接口

### 请求

**主题**: `vendor/callback` | **QoS**: 1

```json
{
  "device_id": "DOLL_001",
  "user_input": "你好，我今天心情不好",
  "vendor_id": "ABC_TOYS",
  "timestamp": 1640995200000
}
```

### 响应

**主题**: `vendor/{device_id}/response` | **QoS**: 1

```json
{
  "device_id": "DOLL_001",
  "response_text": "哦，怎么了？想和我聊聊吗？",
  "screen": {
    "image_url": "https://assets.ai-doll.cn/emotions/sad.gif",
    "image_timer": 100,
    "image_counter": 30,
    "image_mode": "once"
  },
  "servos": {
    "left_ear": [-1, 0, 1, 0],
    "right_ear": [0, 0, 0, 0],
    "tail": [-1, 0, -1, 0]
  },
  "timestamp": 1640995200000
}
```

## 设备心跳接口

### 请求

**主题**: `vendor/heartbeat` | **QoS**: 0

```json
{
  "device_id": "DOLL_001",
  "vendor_id": "ABC_TOYS",
  "battery_level": 90,
  "wifi_strength": 0.95,
  "timestamp": 1640995200000
}
```

### 响应

**主题**: `vendor/{device_id}/heartbeat_ack` | **QoS**: 0

```json
{
  "status": "ok",
  "server_time": "2024-01-01T12:00:00.000Z",
  "device_id": "DOLL_001"
}
```

## 错误消息

**主题**: `vendor/{device_id}/error` | **QoS**: 1

```json
{
  "error": "处理失败",
  "message": "设备ID不存在",
  "code": 404,
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 硬件控制协议

### 屏幕控制

```json
{
  "image_urls": ["string", "string", ...],  // 多个图片文件URL
  "image_timer": "number",      // 每帧间隔时间(毫秒)
  "image_mode": "string"        // 播放模式: "once" | "loop"
}
```

### 舵机控制

```json
{
  "left_ear": "number[]",       // 左耳动作序列
  "right_ear": "number[]",      // 右耳动作序列
  "tail": "number[]"            // 尾巴动作序列
}
```
