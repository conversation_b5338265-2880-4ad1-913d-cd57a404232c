-- 数据库迁移脚本：将 vendor 字段更名为 xstar
-- 执行前请备份数据库！

-- 1. 检查 device_vendors 表是否存在
SELECT 'Checking device_vendors table...' as status;

-- 2. 如果表存在且有 vendor_id 字段，则进行迁移
-- 首先添加新字段
ALTER TABLE `device_vendors` 
ADD COLUMN `xstar_id` VARCHAR(50) AFTER `id`;

-- 复制数据到新字段
UPDATE `device_vendors` 
SET `xstar_id` = `vendor_id` 
WHERE `vendor_id` IS NOT NULL;

-- 设置新字段为 NOT NULL
ALTER TABLE `device_vendors` 
MODIFY COLUMN `xstar_id` VARCHAR(50) NOT NULL COMMENT '厂商唯一标识';

-- 添加唯一索引
ALTER TABLE `device_vendors` 
ADD UNIQUE KEY `idx_xstar_id` (`xstar_id`);

-- 删除旧字段的索引
ALTER TABLE `device_vendors` 
DROP INDEX `idx_vendor_id`;

-- 删除旧字段
ALTER TABLE `device_vendors` 
DROP COLUMN `vendor_id`;

-- 3. 更新设备表中的厂商ID引用（如果存在）
-- 注意：这里假设设备表的 cooldown_policy 字段中可能包含 vendor_id
UPDATE `devices` 
SET `cooldown_policy` = JSON_SET(
    COALESCE(`cooldown_policy`, '{}'), 
    '$.xstar_id', 
    JSON_UNQUOTE(JSON_EXTRACT(`cooldown_policy`, '$.vendor_id'))
)
WHERE JSON_EXTRACT(`cooldown_policy`, '$.vendor_id') IS NOT NULL;

-- 移除旧的 vendor_id 字段
UPDATE `devices` 
SET `cooldown_policy` = JSON_REMOVE(`cooldown_policy`, '$.vendor_id')
WHERE JSON_EXTRACT(`cooldown_policy`, '$.vendor_id') IS NOT NULL;

-- 4. 验证迁移结果
SELECT 'Migration completed. Verifying results...' as status;

-- 检查新字段
SELECT COUNT(*) as total_vendors, 
       COUNT(DISTINCT xstar_id) as unique_xstar_ids
FROM `device_vendors`;

-- 显示迁移后的数据样例
SELECT id, xstar_id, name, status 
FROM `device_vendors` 
LIMIT 5;

SELECT 'Migration verification completed.' as status;
