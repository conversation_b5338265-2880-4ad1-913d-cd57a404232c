# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage
.nyc_output

# 依赖目录
.npm
.eslintcache

# 可选的npm缓存目录
.npm

# 可选的REPL历史
.node_repl_history

# 输出的编译目录
dist
build

# TypeScript缓存
*.tsbuildinfo

# 可选的ESLint缓存
.eslintcache

# 微服务
.serverless

# FuseBox缓存
.fusebox

# DynamoDB本地
.dynamodb

# TernJS端口文件
.tern-port

# 存储在Webpack中的环境变量
.env.local

# parcel-bundler缓存
.cache
.parcel-cache

# next.js构建输出
.next

# nuxt.js构建输出
.nuxt

# vuepress构建输出
.vuepress/dist

# Serverless目录
.serverless

# IDE文件
.vscode
.idea
*.swp
*.swo
*~

# OS生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
README.md

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# 文档
*.md
docs/

# 测试文件
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts
