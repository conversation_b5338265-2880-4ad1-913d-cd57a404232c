{"mqtt": {"host": "**************", "port": 10079, "protocol": "mqtt", "clean": true, "keepalive": 60, "reconnectPeriod": 5000, "connectTimeout": 30000}, "vendors": {"ABC_TOYS": {"username": "vendor_ABC_TOYS", "password": "abc123456789abcdef123456789abcdef12345678", "clientId": "ai_backend_ABC_TOYS"}, "DEF_TOYS": {"username": "vendor_DEF_TOYS", "password": "def987654321fedcba987654321fedcba98765432", "clientId": "ai_backend_DEF_TOYS"}}, "topics": {"subscribe": ["vendor/callback", "vendor/heartbeat"], "publish": {"response": "vendor/{device_id}/response", "heartbeat_ack": "vendor/{device_id}/heartbeat_ack", "error": "vendor/{device_id}/error"}}, "ai": {"deepseek": {"enabled": true, "max_tokens": 150, "temperature": 0.8, "response_max_length": 50}, "fallback_responses": ["我在这里陪着你呢~", "我现在有点累了，但我很开心能和你聊天~", "让我想想该怎么回答你...", "你说得真有趣，我需要思考一下~"]}, "emotions": {"happy": {"keywords": ["开心", "高兴", "哈哈", "棒", "好的", "太好了", "喜欢"], "screen": {"image_url": "https://assets.ai-doll.cn/emotions/happy.gif", "image_timer": 100, "image_counter": 30, "image_mode": "once"}, "servos": {"left_ear": [1, 0, 1, 0], "right_ear": [1, 0, 1, 0], "tail": [1, 1, 0, 0]}}, "sad": {"keywords": ["难过", "伤心", "不开心", "哭", "痛苦", "失望"], "screen": {"image_url": "https://assets.ai-doll.cn/emotions/sad.gif", "image_timer": 150, "image_counter": 20, "image_mode": "once"}, "servos": {"left_ear": [-1, 0, -1, 0], "right_ear": [-1, 0, -1, 0], "tail": [0, 0, 0, 0]}}, "excited": {"keywords": ["激动", "兴奋", "哇", "太棒了", "厉害", "惊喜"], "screen": {"image_url": "https://assets.ai-doll.cn/emotions/excited.gif", "image_timer": 80, "image_counter": 40, "image_mode": "loop"}, "servos": {"left_ear": [1, -1, 1, -1], "right_ear": [1, -1, 1, -1], "tail": [1, 0, 1, 0]}}, "default": {"keywords": [], "screen": {"image_url": "https://assets.ai-doll.cn/emotions/neutral.gif", "image_timer": 120, "image_counter": 25, "image_mode": "once"}, "servos": {"left_ear": [0, 1, 0, -1], "right_ear": [0, -1, 0, 1], "tail": [0, 0, 1, 0]}}}, "vendor_info": {"ABC_TOYS": {"name": "ABC玩具公司", "status": "active", "devices": ["TEST_DOLL_001", "DOLL_002", "DOLL_003"]}, "DEF_TOYS": {"name": "DEF玩具公司", "status": "active", "devices": ["TEST_DEF_001", "DEF_DEVICE_002"]}}, "logging": {"level": "info", "log_file": "logs/vendor-mqtt-client.log", "max_file_size": "10MB", "max_files": 5}}