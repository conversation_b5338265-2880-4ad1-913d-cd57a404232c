# ABC玩具公司设备配置说明

## 📱 设备信息

### 基本信息
- **厂商**: ABC玩具公司 (ABC_TOYS)
- **设备ID**: `DOLL_001`
- **设备名称**: ABC玩具公司智能娃娃
- **设备类型**: 智能对话娃娃
- **状态**: 在线 (online)

### 技术规格
- **AI模型**: qwen-turbo (阿里云通义千问)
- **角色**: 小白 (可爱宠物角色)
- **MQTT主题前缀**: xstar
- **厂商标识**: ABC_TOYS

## 🔧 MQTT配置

### 连接信息
```
服务器: **************
端口: 10079
协议: MQTT 3.1.1
客户端ID: xstar_device_DOLL_001
用户名: xstar_ABC_TOYS
密码: [厂商API密钥]
```

### 消息主题

#### 发布主题（设备→服务器）
- **对话回调**: `xstar/callback`
- **设备心跳**: `xstar/heartbeat`

#### 订阅主题（服务器→设备）
- **AI回复**: `xstar/DOLL_001/response`
- **心跳确认**: `xstar/DOLL_001/heartbeat_ack`
- **错误消息**: `xstar/DOLL_001/error`

## 📡 消息格式

### 对话回调消息
```json
{
  "device_id": "DOLL_001",
  "user_input": "你好，我今天心情不好",
  "xstar_id": "ABC_TOYS",
  "timestamp": 1640995200000
}
```

### AI回复消息
```json
{
  "device_id": "DOLL_001",
  "response_text": "哦，怎么了？想和我聊聊吗？",
  "screen": {
    "image_url": "https://assets.ai-doll.cn/emotions/sad.gif",
    "image_timer": 100,
    "image_counter": 30,
    "image_mode": "once"
  },
  "servos": {
    "left_ear": [-1, 0, 1, 0],
    "right_ear": [0, 0, 0, 0],
    "tail": [-1, 0, -1, 0]
  },
  "timestamp": 1640995200000
}
```

### 心跳消息
```json
{
  "device_id": "DOLL_001",
  "xstar_id": "ABC_TOYS",
  "battery_level": 85,
  "wifi_strength": 0.92,
  "timestamp": 1640995200000
}
```

### 心跳确认
```json
{
  "status": "ok",
  "server_time": "2024-01-01T12:00:00.000Z",
  "device_id": "DOLL_001"
}
```

## 🎭 角色配置

### 小白角色特性
```json
{
  "name": "小白",
  "type": "pet",
  "personality": {
    "friendliness": 95,
    "intelligence": 70,
    "activeness": 85,
    "curiosity": 90,
    "humor": 75,
    "empathy": 88
  },
  "prompt_template": "你是一只可爱的小白狗，性格活泼友善，喜欢和主人玩耍。你会用温暖的语气回应主人，偶尔会撒娇。"
}
```

### 硬件控制映射

#### 情感表达
- **开心**: 耳朵上下摆动，尾巴快速摇摆
- **难过**: 耳朵下垂，尾巴静止
- **兴奋**: 耳朵和尾巴快速摆动
- **平静**: 所有部件保持中性位置

#### 屏幕显示
- **开心**: happy.gif
- **难过**: sad.gif
- **兴奋**: excited.gif
- **平静**: neutral.gif

## 🔐 安全配置

### API密钥
- **厂商API密钥**: `abc123456789abcdef123456789abcdef12345678`
- **权限范围**: 设备DOLL_001的完整控制权限
- **有效期**: 永久（可重新生成）

### 访问控制
- 只有ABC_TOYS厂商可以控制DOLL_001设备
- 设备数据归属于租户1（ABC玩具公司租户）
- 用户tenant1可以管理此设备

## 📊 监控信息

### 设备状态
```json
{
  "device_id": "DOLL_001",
  "status": "online",
  "last_heartbeat": "2024-01-01T12:00:00.000Z",
  "battery_level": 85,
  "wifi_strength": 0.92,
  "hardware_info": {
    "screen": {
      "status": "normal",
      "current_image": "https://assets.ai-doll.cn/emotions/neutral.gif"
    },
    "servos": {
      "left_ear": { "position": 0, "status": "normal" },
      "right_ear": { "position": 0, "status": "normal" },
      "tail": { "position": 0, "status": "normal" }
    }
  }
}
```

### 性能指标
- **平均响应时间**: < 150ms
- **消息成功率**: > 99%
- **在线时间**: > 99.5%
- **电池续航**: 8-12小时

## 🧠 记忆系统

### 自动记忆提取
设备DOLL_001会自动从对话中提取以下类型的记忆：

1. **用户偏好** (preference)
   - 喜欢的活动、食物、颜色等
   - 重要性评分: 7-9

2. **事实信息** (fact)
   - 用户的基本信息、家庭情况等
   - 重要性评分: 8-10

3. **情感状态** (emotion)
   - 用户的情绪变化、心情记录
   - 重要性评分: 6-8

4. **行为习惯** (habit)
   - 用户的日常习惯、作息时间等
   - 重要性评分: 5-7

### 记忆利用
- 对话时自动检索相关记忆
- 根据记忆个性化回复
- 记忆会影响角色的行为表现

## 🔧 开发测试

### 本地测试命令
```bash
# 测试设备连接
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:3000/api/enhanced-devices/DOLL_001

# 发送测试消息
curl -X POST http://localhost:3000/api/enhanced-chat/send \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "device_id": "DOLL_001",
    "message": "你好小白，今天天气真好！"
  }'

# 查看对话历史
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:3000/api/enhanced-chat/history/DOLL_001?limit=10"

# 查看设备状态
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:3000/api/external-mqtt/devices/DOLL_001/hardware
```

### MQTT测试工具
```bash
# 使用mosquitto客户端测试
mosquitto_pub -h ************** -p 10079 \
  -u xstar_ABC_TOYS -P [API_KEY] \
  -t xstar/callback \
  -m '{"device_id":"DOLL_001","user_input":"测试消息","xstar_id":"ABC_TOYS","timestamp":1640995200000}'

# 订阅回复消息
mosquitto_sub -h ************** -p 10079 \
  -u xstar_ABC_TOYS -P [API_KEY] \
  -t xstar/DOLL_001/response
```

## 📞 技术支持

### 联系信息
- **厂商**: ABC玩具公司
- **邮箱**: <EMAIL>
- **电话**: +86-400-123-4567
- **地址**: 深圳市南山区科技园

### 常见问题
1. **设备离线**: 检查网络连接和MQTT服务器状态
2. **消息无响应**: 验证API密钥和设备权限
3. **硬件控制异常**: 检查舵机连接和电源状态
4. **电池电量低**: 及时充电，电量低于20%时会影响性能

## 🎉 总结

DOLL_001是ABC玩具公司的旗舰智能娃娃产品，具备：
- ✅ 完整的AI对话功能
- ✅ 智能记忆系统
- ✅ 硬件控制能力
- ✅ 实时状态监控
- ✅ 安全的权限管理

设备已完全集成到多用户AI对话平台，可以为用户提供个性化的AI陪伴体验。
