const mqtt = require('mqtt');
const fs = require('fs');
const path = require('path');
const DeepSeekService = require('./deepseekService');
const DynamicPromptService = require('./dynamicPromptService');
const logBroadcastService = require('./logBroadcastService');

class VendorMqttClientService {
    constructor(vendorId = 'ABC_TOYS') {
        this.vendorId = vendorId;
        this.client = null;
        this.deepSeekService = new DeepSeekService();
        this.promptService = new DynamicPromptService();
        this.isConnected = false;
        
        // 加载配置文件
        this.config = this.loadConfig();
        this.connectionOptions = this.buildConnectionOptions();
        
        // 缓存AI回复的情感动作映射
        this.emotionActions = {
            happy: {
                screen: {
                    image_url: "https://assets.ai-doll.cn/emotions/happy.gif",
                    image_timer: 100,
                    image_counter: 30,
                    image_mode: "once"
                },
                servos: {
                    left_ear: [1, 0, 1, 0],
                    right_ear: [1, 0, 1, 0], 
                    tail: [1, 1, 0, 0]
                }
            },
            sad: {
                screen: {
                    image_url: "https://assets.ai-doll.cn/emotions/sad.gif",
                    image_timer: 150,
                    image_counter: 20,
                    image_mode: "once"
                },
                servos: {
                    left_ear: [-1, 0, -1, 0],
                    right_ear: [-1, 0, -1, 0],
                    tail: [0, 0, 0, 0]
                }
            },
            excited: {
                screen: {
                    image_url: "https://assets.ai-doll.cn/emotions/excited.gif",
                    image_timer: 80,
                    image_counter: 40,
                    image_mode: "loop"
                },
                servos: {
                    left_ear: [1, -1, 1, -1],
                    right_ear: [1, -1, 1, -1],
                    tail: [1, 0, 1, 0]
                }
            },
            default: {
                screen: {
                    image_url: "https://assets.ai-doll.cn/emotions/neutral.gif",
                    image_timer: 120,
                    image_counter: 25,
                    image_mode: "once"
                },
                servos: {
                    left_ear: [0, 1, 0, -1],
                    right_ear: [0, -1, 0, 1],
                    tail: [0, 0, 1, 0]
                }
            }
        };
    }

    // 加载配置文件
    loadConfig() {
        try {
            const configPath = path.join(__dirname, '../../config/vendor-mqtt-config.json');
            const configData = fs.readFileSync(configPath, 'utf8');
            return JSON.parse(configData);
        } catch (error) {
            console.error('加载配置文件失败:', error);
            throw new Error('无法加载厂商MQTT配置文件');
        }
    }

    // 构建连接选项
    buildConnectionOptions() {
        const mqttConfig = this.config.mqtt;
        const vendorConfig = this.config.vendors[this.vendorId];
        
        if (!vendorConfig) {
            throw new Error(`未找到厂商 ${this.vendorId} 的配置信息`);
        }

        return {
            host: mqttConfig.host,
            port: mqttConfig.port,
            protocol: mqttConfig.protocol || 'mqtt',
            username: vendorConfig.username,
            password: vendorConfig.password,
            clientId: vendorConfig.clientId,
            clean: mqttConfig.clean,
            keepalive: mqttConfig.keepalive,
            reconnectPeriod: 0, // 禁用自动重连，手动控制
            connectTimeout: 8000 // 设置8秒连接超时
        };
    }

    // 连接厂商MQTT服务器
    async connect() {
        return new Promise((resolve, reject) => {
            try {
                // 广播连接开始日志
                logBroadcastService.broadcastConnectionStart(
                    this.vendorId,
                    this.connectionOptions.host,
                    this.connectionOptions.port,
                    this.connectionOptions.username
                );
                logBroadcastService.broadcastLog(this.vendorId, 'info', `连接参数: 超时${this.connectionOptions.connectTimeout}ms, 保活${this.connectionOptions.keepalive}s`);
                
                this.client = mqtt.connect(this.connectionOptions);
                this.lastError = null;
                this.connectionStatus = 'connecting';
                this.connectionStartTime = Date.now();
                
                let connectionProgress = 0;
                
                // 显示连接进度
                const progressInterval = setInterval(() => {
                    connectionProgress += 1;
                    const elapsed = Date.now() - this.connectionStartTime;
                    logBroadcastService.broadcastConnectionProgress(this.vendorId, connectionProgress, elapsed);
                }, 1000);
                
                // 设置连接超时 - 缩短到8秒
                const connectTimeout = setTimeout(() => {
                    clearInterval(progressInterval);
                    const elapsed = Date.now() - this.connectionStartTime;
                    this.lastError = `连接超时 (${elapsed}ms) - 服务器无响应`;
                    this.connectionStatus = 'failed';
                    this.isConnected = false;
                    
                    // 广播连接超时
                    logBroadcastService.broadcastConnectionTimeout(
                        this.vendorId,
                        elapsed,
                        this.connectionOptions.host,
                        this.connectionOptions.port
                    );
                    
                    if (this.client) {
                        this.client.end(true);
                    }
                    reject(new Error(this.lastError));
                }, 8000); // 缩短超时时间到8秒
                
                this.client.on('connect', (connack) => {
                    clearTimeout(connectTimeout);
                    clearInterval(progressInterval);
                    const elapsed = Date.now() - this.connectionStartTime;
                    
                    // 广播连接成功
                    logBroadcastService.broadcastConnectionSuccess(this.vendorId, elapsed, connack);
                    
                    this.isConnected = true;
                    this.connectionStatus = 'connected';
                    this.lastError = null;
                    this.subscribeToTopics();
                    resolve(this.client);
                });

                this.client.on('message', (topic, message) => {
                    this.handleMessage(topic, message);
                });

                this.client.on('error', (error) => {
                    clearTimeout(connectTimeout);
                    clearInterval(progressInterval);
                    const elapsed = Date.now() - this.connectionStartTime;
                    
                    this.lastError = error.message || error.toString();
                    this.connectionStatus = 'error';
                    this.isConnected = false;
                    
                    // 根据错误类型提供具体诊断和建议
                    let diagnosis = '';
                    let suggestion = '';
                    
                    if (error.message.includes('ENOTFOUND')) {
                        diagnosis = 'DNS解析失败 - 服务器地址不存在';
                        suggestion = `检查服务器地址 '${this.connectionOptions.host}' 是否正确`;
                    } else if (error.message.includes('ECONNREFUSED')) {
                        diagnosis = '连接被拒绝 - 端口未开放或服务未运行';
                        suggestion = `检查端口 ${this.connectionOptions.port} 和服务器状态`;
                    } else if (error.message.includes('ETIMEDOUT')) {
                        diagnosis = '网络超时 - 防火墙或网络问题';
                        suggestion = '检查网络连接和防火墙设置';
                    } else if (error.message.includes('connack timeout')) {
                        diagnosis = 'MQTT握手超时 - 服务器无响应或认证问题';
                        suggestion = '检查认证信息或联系厂商确认服务器状态';
                    } else {
                        diagnosis = '未知错误类型';
                        suggestion = '联系厂商或系统管理员';
                    }
                    
                    // 广播连接失败
                    logBroadcastService.broadcastConnectionError(
                        this.vendorId,
                        error,
                        elapsed,
                        diagnosis,
                        suggestion
                    );
                    
                    // 强制结束客户端，避免悬挂
                    if (this.client) {
                        this.client.end(true);
                    }
                    
                    reject(error);
                });

                this.client.on('close', () => {
                    clearInterval(progressInterval);
                    logBroadcastService.broadcastEvent(this.vendorId, 'disconnect', 'MQTT连接已断开');
                    this.isConnected = false;
                    if (this.connectionStatus !== 'failed' && this.connectionStatus !== 'error') {
                        this.connectionStatus = 'disconnected';
                    }
                });

                this.client.on('reconnect', () => {
                    logBroadcastService.broadcastEvent(this.vendorId, 'reconnect', '尝试重新连接MQTT服务器...');
                    this.connectionStatus = 'reconnecting';
                });

                this.client.on('offline', () => {
                    logBroadcastService.broadcastEvent(this.vendorId, 'offline', '客户端离线');
                    this.isConnected = false;
                    this.connectionStatus = 'offline';
                });

                // 处理底层TCP连接事件
                this.client.stream.on('connect', () => {
                    logBroadcastService.broadcastEvent(this.vendorId, 'tcp-connect', 'TCP连接已建立，等待MQTT握手...');
                });

                this.client.stream.on('timeout', () => {
                    logBroadcastService.broadcastEvent(this.vendorId, 'tcp-connect', 'TCP连接超时');
                });

            } catch (error) {
                this.lastError = error.message;
                this.connectionStatus = 'error';
                console.error(`💥 [${this.vendorId}] 连接初始化失败:`, error);
                reject(error);
            }
        });
    }

    // 订阅相关主题
    subscribeToTopics() {
        const topics = [
            'vendor/callback',      // 对话回调
            'vendor/heartbeat'      // 设备心跳
        ];

        topics.forEach(topic => {
            this.client.subscribe(topic, { qos: 1 }, (err) => {
                if (err) {
                    console.error(`❌ 订阅主题失败 ${topic}:`, err);
                } else {
                    console.log(`✅ 成功订阅主题: ${topic}`);
                }
            });
        });
    }

    // 处理接收到的消息
    async handleMessage(topic, message) {
        try {
            const data = JSON.parse(message.toString());
            console.log(`📨 收到消息 [${topic}]:`, data);

            switch (topic) {
                case 'vendor/callback':
                    await this.handleDialogueCallback(data);
                    break;
                case 'vendor/heartbeat':
                    await this.handleHeartbeat(data);
                    break;
                default:
                    console.log(`⚠️ 未知主题: ${topic}`);
            }
        } catch (error) {
            console.error('处理消息时出错:', error);
            // 发送错误消息给设备
            if (topic === 'vendor/callback' && message) {
                const data = JSON.parse(message.toString());
                this.sendErrorMessage(data.device_id, '消息处理失败', 500);
            }
        }
    }

    // 处理对话回调
    async handleDialogueCallback(data) {
        try {
            const { device_id, user_input, vendor_id, timestamp } = data;
            
            console.log(`🎯 处理设备 ${device_id} 的对话请求:`, user_input);

            // 使用DeepSeek AI生成回复
            const aiResponse = await this.generateAIResponse(user_input, device_id, vendor_id);
            
            // 根据AI回复内容选择合适的情感动作
            const emotion = this.analyzeEmotion(aiResponse.response_text);
            const actions = this.emotionActions[emotion] || this.emotionActions.default;

            // 构造完整的回复消息
            const response = {
                device_id: device_id,
                response_text: aiResponse.response_text,
                screen: actions.screen,
                servos: actions.servos,
                timestamp: Date.now()
            };

            // 发送回复到设备
            const responseTopic = `vendor/${device_id}/response`;
            await this.publishMessage(responseTopic, response, 1);
            
            console.log(`✅ 已发送AI回复到设备 ${device_id}`);

        } catch (error) {
            console.error('处理对话回调失败:', error);
            this.sendErrorMessage(data.device_id, '对话处理失败', 500);
        }
    }

    // 处理设备心跳
    async handleHeartbeat(data) {
        try {
            const { device_id, vendor_id, battery_level, wifi_strength, timestamp } = data;
            
            console.log(`💓 设备心跳 ${device_id}: 电量${battery_level}%, WiFi${Math.round(wifi_strength * 100)}%`);

            // 发送心跳确认
            const ackResponse = {
                status: "ok",
                server_time: new Date().toISOString(),
                device_id: device_id
            };

            const ackTopic = `vendor/${device_id}/heartbeat_ack`;
            await this.publishMessage(ackTopic, ackResponse, 0);
            
            console.log(`✅ 已发送心跳确认到设备 ${device_id}`);

        } catch (error) {
            console.error('处理设备心跳失败:', error);
        }
    }

    // 使用DeepSeek生成AI回复
    async generateAIResponse(userInput, deviceId, vendorId) {
        try {
            // 获取动态Prompt
            const promptData = await this.promptService.getPromptForVendor(vendorId);
            
            // 构建完整的prompt
            const fullPrompt = `${promptData.systemPrompt}\n\n用户说: ${userInput}\n\n请给出温暖、友好的回复，长度控制在50字以内。`;
            
            // 调用DeepSeek API
            const response = await this.deepSeekService.generateResponse(fullPrompt, {
                max_tokens: 150,
                temperature: 0.8
            });

            return {
                response_text: response.content || response.message || '我在这里陪着你呢~',
                emotion: this.analyzeEmotion(response.content || '')
            };

        } catch (error) {
            console.error('AI回复生成失败:', error);
            // 返回默认回复
            return {
                response_text: '我现在有点累了，但我很开心能和你聊天~',
                emotion: 'default'
            };
        }
    }

    // 分析情感
    analyzeEmotion(text) {
        if (!text) return 'default';
        
        const happyKeywords = ['开心', '高兴', '哈哈', '棒', '好的', '太好了', '喜欢'];
        const sadKeywords = ['难过', '伤心', '不开心', '哭', '痛苦', '失望'];
        const excitedKeywords = ['激动', '兴奋', '哇', '太棒了', '厉害', '惊喜'];
        
        if (happyKeywords.some(keyword => text.includes(keyword))) return 'happy';
        if (sadKeywords.some(keyword => text.includes(keyword))) return 'sad';
        if (excitedKeywords.some(keyword => text.includes(keyword))) return 'excited';
        
        return 'default';
    }

    // 发布消息
    async publishMessage(topic, message, qos = 0) {
        if (!this.isConnected || !this.client) {
            throw new Error('MQTT客户端未连接');
        }

        return new Promise((resolve, reject) => {
            this.client.publish(topic, JSON.stringify(message), { qos }, (error) => {
                if (error) {
                    console.error(`发布消息失败 [${topic}]:`, error);
                    reject(error);
                } else {
                    console.log(`📤 已发布消息 [${topic}]:`, message);
                    resolve();
                }
            });
        });
    }

    // 发送错误消息
    async sendErrorMessage(deviceId, errorMessage, errorCode) {
        try {
            const errorResponse = {
                error: "处理失败",
                message: errorMessage,
                code: errorCode,
                timestamp: new Date().toISOString()
            };

            const errorTopic = `vendor/${deviceId}/error`;
            await this.publishMessage(errorTopic, errorResponse, 1);
            
        } catch (error) {
            console.error('发送错误消息失败:', error);
        }
    }

    // 断开连接
    disconnect() {
        if (this.client) {
            console.log('正在断开MQTT连接...');
            this.client.end();
            this.isConnected = false;
            console.log('✅ MQTT连接已断开');
        }
    }

    // 获取连接状态
    getStatus() {
        return {
            connected: this.isConnected,
            clientId: this.connectionOptions.clientId,
            server: `${this.connectionOptions.host}:${this.connectionOptions.port}`,
            status: this.connectionStatus || 'unknown',
            lastError: this.lastError || null,
            vendorId: this.vendorId
        };
    }

    // 更新连接配置
    updateConnectionConfig(config) {
        this.connectionOptions = {
            ...this.connectionOptions,
            ...config
        };
        console.log('连接配置已更新:', this.connectionOptions);
    }
}

module.exports = { VendorMqttClientService }; 
