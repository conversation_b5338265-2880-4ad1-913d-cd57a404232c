import { pool } from '../config/database'
import redis from '../utils/redis'

// 记忆类型枚举
export enum MemoryType {
  PREFERENCE = 'preference',  // 偏好
  FACT = 'fact',             // 事实
  EMOTION = 'emotion',       // 情感
  HABIT = 'habit'            // 习惯
}

// 记忆接口
export interface Memory {
  id?: number
  user_id: number
  character_id: number
  memory_type: MemoryType
  content: string
  importance: number
  last_accessed: Date
  access_count: number
  created_at?: Date
  updated_at?: Date
}

// 记忆搜索结果
export interface MemorySearchResult {
  memories: Memory[]
  total: number
  relevantMemories: Memory[]
}

export class MemoryService {
  /**
   * 添加记忆
   */
  static async addMemory(
    userId: number,
    characterId: number,
    memoryType: MemoryType,
    content: string,
    importance: number = 5
  ): Promise<number> {
    try {
      // 检查是否已存在相似记忆
      const existingMemory = await this.findSimilarMemory(userId, characterId, content)
      
      if (existingMemory) {
        // 更新现有记忆的重要性和访问次数
        await this.updateMemoryImportance(existingMemory.id!, Math.max(existingMemory.importance, importance))
        return existingMemory.id!
      }

      // 创建新记忆
      const [result]: any = await pool.execute(
        `INSERT INTO user_memories (user_id, character_id, memory_type, content, importance, last_accessed, access_count) 
         VALUES (?, ?, ?, ?, ?, NOW(), 1)`,
        [userId, characterId, memoryType, content, importance]
      )

      // 清除相关缓存
      await this.clearMemoryCache(userId, characterId)

      return result.insertId
    } catch (error) {
      console.error('添加记忆失败:', error)
      throw error
    }
  }

  /**
   * 查找相似记忆
   */
  private static async findSimilarMemory(
    userId: number,
    characterId: number,
    content: string
  ): Promise<Memory | null> {
    try {
      const [rows]: any = await pool.execute(
        `SELECT * FROM user_memories 
         WHERE user_id = ? AND character_id = ? AND content LIKE ? 
         ORDER BY importance DESC, last_accessed DESC 
         LIMIT 1`,
        [userId, characterId, `%${content.substring(0, 50)}%`]
      )

      return rows.length > 0 ? rows[0] : null
    } catch (error) {
      console.error('查找相似记忆失败:', error)
      return null
    }
  }

  /**
   * 获取用户与角色的记忆
   */
  static async getMemories(
    userId: number,
    characterId: number,
    memoryType?: MemoryType,
    limit: number = 50
  ): Promise<Memory[]> {
    try {
      // 尝试从缓存获取
      const cacheKey = `memories:${userId}:${characterId}:${memoryType || 'all'}:${limit}`
      const cached = await redis.get(cacheKey)
      
      if (cached) {
        return JSON.parse(cached)
      }

      let query = `
        SELECT * FROM user_memories 
        WHERE user_id = ? AND character_id = ?
      `
      const params: any[] = [userId, characterId]

      if (memoryType) {
        query += ' AND memory_type = ?'
        params.push(memoryType)
      }

      query += ' ORDER BY importance DESC, last_accessed DESC LIMIT ?'
      params.push(limit)

      const [rows]: any = await pool.execute(query, params)

      // 缓存结果
      await redis.setex(cacheKey, 300, JSON.stringify(rows)) // 5分钟缓存

      return rows
    } catch (error) {
      console.error('获取记忆失败:', error)
      return []
    }
  }

  /**
   * 搜索相关记忆
   */
  static async searchRelevantMemories(
    userId: number,
    characterId: number,
    query: string,
    limit: number = 10
  ): Promise<Memory[]> {
    try {
      const keywords = query.split(' ').filter(word => word.length > 1)
      
      if (keywords.length === 0) {
        return []
      }

      // 构建搜索条件
      const searchConditions = keywords.map(() => 'content LIKE ?').join(' OR ')
      const searchParams = keywords.map(keyword => `%${keyword}%`)

      const [rows]: any = await pool.execute(
        `SELECT *, 
         (importance * 0.7 + access_count * 0.2 + 
          CASE WHEN last_accessed > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 0.1 ELSE 0 END) as relevance_score
         FROM user_memories 
         WHERE user_id = ? AND character_id = ? AND (${searchConditions})
         ORDER BY relevance_score DESC, last_accessed DESC 
         LIMIT ?`,
        [userId, characterId, ...searchParams, limit]
      )

      // 更新访问记录
      for (const memory of rows) {
        await this.updateMemoryAccess(memory.id)
      }

      return rows
    } catch (error) {
      console.error('搜索相关记忆失败:', error)
      return []
    }
  }

  /**
   * 更新记忆访问记录
   */
  private static async updateMemoryAccess(memoryId: number): Promise<void> {
    try {
      await pool.execute(
        'UPDATE user_memories SET access_count = access_count + 1, last_accessed = NOW() WHERE id = ?',
        [memoryId]
      )
    } catch (error) {
      console.error('更新记忆访问记录失败:', error)
    }
  }

  /**
   * 更新记忆重要性
   */
  private static async updateMemoryImportance(memoryId: number, importance: number): Promise<void> {
    try {
      await pool.execute(
        'UPDATE user_memories SET importance = ?, access_count = access_count + 1, last_accessed = NOW() WHERE id = ?',
        [importance, memoryId]
      )
    } catch (error) {
      console.error('更新记忆重要性失败:', error)
    }
  }

  /**
   * 从对话中提取记忆
   */
  static async extractMemoriesFromChat(
    userId: number,
    characterId: number,
    userInput: string,
    aiResponse: string
  ): Promise<void> {
    try {
      // 简单的记忆提取逻辑
      const memories = this.analyzeTextForMemories(userInput, aiResponse)
      
      for (const memory of memories) {
        await this.addMemory(userId, characterId, memory.type, memory.content, memory.importance)
      }
    } catch (error) {
      console.error('从对话中提取记忆失败:', error)
    }
  }

  /**
   * 分析文本提取记忆点
   */
  private static analyzeTextForMemories(userInput: string, aiResponse: string): Array<{
    type: MemoryType
    content: string
    importance: number
  }> {
    const memories: Array<{ type: MemoryType; content: string; importance: number }> = []

    // 偏好识别
    const preferencePatterns = [
      /我喜欢(.+)/g,
      /我不喜欢(.+)/g,
      /我最爱(.+)/g,
      /我讨厌(.+)/g,
      /我偏好(.+)/g
    ]

    for (const pattern of preferencePatterns) {
      const matches = userInput.matchAll(pattern)
      for (const match of matches) {
        memories.push({
          type: MemoryType.PREFERENCE,
          content: `用户${match[0]}`,
          importance: 7
        })
      }
    }

    // 事实识别
    const factPatterns = [
      /我是(.+)/g,
      /我在(.+)工作/g,
      /我住在(.+)/g,
      /我的(.+)是(.+)/g,
      /我今年(.+)岁/g
    ]

    for (const pattern of factPatterns) {
      const matches = userInput.matchAll(pattern)
      for (const match of matches) {
        memories.push({
          type: MemoryType.FACT,
          content: `用户${match[0]}`,
          importance: 8
        })
      }
    }

    // 情感识别
    const emotionPatterns = [
      /我感到(.+)/g,
      /我很(.+)/g,
      /让我(.+)/g,
      /我觉得(.+)/g
    ]

    for (const pattern of emotionPatterns) {
      const matches = userInput.matchAll(pattern)
      for (const match of matches) {
        memories.push({
          type: MemoryType.EMOTION,
          content: `用户${match[0]}`,
          importance: 6
        })
      }
    }

    // 习惯识别
    const habitPatterns = [
      /我经常(.+)/g,
      /我总是(.+)/g,
      /我习惯(.+)/g,
      /我每天(.+)/g,
      /我通常(.+)/g
    ]

    for (const pattern of habitPatterns) {
      const matches = userInput.matchAll(pattern)
      for (const match of matches) {
        memories.push({
          type: MemoryType.HABIT,
          content: `用户${match[0]}`,
          importance: 7
        })
      }
    }

    return memories
  }

  /**
   * 清理过期记忆
   */
  static async cleanupOldMemories(userId: number, characterId: number): Promise<void> {
    try {
      // 删除30天未访问且重要性低于3的记忆
      await pool.execute(
        `DELETE FROM user_memories 
         WHERE user_id = ? AND character_id = ? 
         AND last_accessed < DATE_SUB(NOW(), INTERVAL 30 DAY) 
         AND importance < 3`,
        [userId, characterId]
      )

      // 清除缓存
      await this.clearMemoryCache(userId, characterId)
    } catch (error) {
      console.error('清理过期记忆失败:', error)
    }
  }

  /**
   * 清除记忆缓存
   */
  private static async clearMemoryCache(userId: number, characterId: number): Promise<void> {
    try {
      const pattern = `memories:${userId}:${characterId}:*`
      const keys = await redis.keys(pattern)
      
      if (keys.length > 0) {
        await redis.del(...keys)
      }
    } catch (error) {
      console.error('清除记忆缓存失败:', error)
    }
  }

  /**
   * 获取记忆统计
   */
  static async getMemoryStats(userId: number, characterId: number): Promise<{
    total: number
    byType: Record<string, number>
    recentlyAccessed: number
  }> {
    try {
      // 总数统计
      const [totalResult]: any = await pool.execute(
        'SELECT COUNT(*) as total FROM user_memories WHERE user_id = ? AND character_id = ?',
        [userId, characterId]
      )

      // 按类型统计
      const [typeResult]: any = await pool.execute(
        'SELECT memory_type, COUNT(*) as count FROM user_memories WHERE user_id = ? AND character_id = ? GROUP BY memory_type',
        [userId, characterId]
      )

      // 最近访问统计
      const [recentResult]: any = await pool.execute(
        'SELECT COUNT(*) as count FROM user_memories WHERE user_id = ? AND character_id = ? AND last_accessed > DATE_SUB(NOW(), INTERVAL 7 DAY)',
        [userId, characterId]
      )

      const byType: Record<string, number> = {}
      typeResult.forEach((row: any) => {
        byType[row.memory_type] = row.count
      })

      return {
        total: totalResult[0].total,
        byType,
        recentlyAccessed: recentResult[0].count
      }
    } catch (error) {
      console.error('获取记忆统计失败:', error)
      return { total: 0, byType: {}, recentlyAccessed: 0 }
    }
  }
}
