import axios from 'axios'

// 阿里云通义千问模型配置
export interface AliyunModelConfig {
  api_key: string
  model: string
  api_url?: string
  temperature?: number
  max_tokens?: number
  top_p?: number
  top_k?: number
}

// 消息接口
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

// 请求接口
export interface AliyunChatRequest {
  messages: ChatMessage[]
  temperature?: number
  max_tokens?: number
  top_p?: number
  top_k?: number
  stream?: boolean
}

// 响应接口
export interface AliyunChatResponse {
  content: string
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
  model: string
  finish_reason?: string
}

// 阿里云模型服务类
export class AliyunModelService {
  private config: AliyunModelConfig

  constructor(config: AliyunModelConfig) {
    this.config = {
      api_url: 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
      temperature: 0.7,
      max_tokens: 2048,
      top_p: 0.8,
      ...config
    }
  }

  /**
   * 调用阿里云通义千问模型
   */
  async chat(request: AliyunChatRequest): Promise<AliyunChatResponse> {
    try {
      const response = await axios.post(
        this.config.api_url!,
        {
          model: this.config.model,
          messages: request.messages,
          temperature: request.temperature ?? this.config.temperature,
          max_tokens: request.max_tokens ?? this.config.max_tokens,
          top_p: request.top_p ?? this.config.top_p,
          stream: request.stream || false
        },
        {
          headers: {
            'Authorization': `Bearer ${this.config.api_key}`,
            'Content-Type': 'application/json'
          },
          timeout: 60000 // 60秒超时
        }
      )

      const data = response.data
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('阿里云API返回数据格式错误')
      }

      return {
        content: data.choices[0].message.content,
        usage: data.usage,
        model: data.model,
        finish_reason: data.choices[0].finish_reason
      }
    } catch (error: any) {
      console.error('阿里云模型调用失败:', error.message)
      
      if (error.response) {
        const errorData = error.response.data
        throw new Error(`阿里云API错误: ${errorData.error?.message || error.message}`)
      }
      
      throw new Error(`阿里云模型调用失败: ${error.message}`)
    }
  }

  /**
   * 构建角色对话的系统提示词
   */
  buildSystemPrompt(characterName: string, characterType: string, personality: any, promptTemplate: string): string {
    const personalityStr = Object.entries(personality)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ')

    return `${promptTemplate}

角色信息:
- 名称: ${characterName}
- 类型: ${characterType}
- 性格特征: ${personalityStr}

请始终保持角色设定，用符合角色性格的方式回复用户。`
  }

  /**
   * 为角色对话生成回复
   */
  async generateCharacterResponse(
    userInput: string,
    characterName: string,
    characterType: string,
    personality: any,
    promptTemplate: string,
    conversationHistory: ChatMessage[] = []
  ): Promise<AliyunChatResponse> {
    const systemPrompt = this.buildSystemPrompt(characterName, characterType, personality, promptTemplate)
    
    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      ...conversationHistory.slice(-10), // 保留最近10轮对话
      { role: 'user', content: userInput }
    ]

    return await this.chat({ messages })
  }

  /**
   * 检查模型健康状态
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.chat({
        messages: [{ role: 'user', content: '你好' }],
        max_tokens: 10
      })
      
      return response.content.length > 0
    } catch (error) {
      console.error('阿里云模型健康检查失败:', error)
      return false
    }
  }

  /**
   * 获取支持的模型列表
   */
  static getSupportedModels(): string[] {
    return [
      'qwen-turbo',
      'qwen-plus',
      'qwen-max',
      'qwen-max-1201',
      'qwen-max-longcontext',
      'qwen2-72b-instruct',
      'qwen2-57b-a14b-instruct',
      'qwen2-7b-instruct',
      'qwen2-1.5b-instruct',
      'qwen2-0.5b-instruct'
    ]
  }

  /**
   * 获取模型信息
   */
  static getModelInfo(model: string): { name: string; description: string; maxTokens: number } {
    const modelInfo: Record<string, { name: string; description: string; maxTokens: number }> = {
      'qwen-turbo': {
        name: '通义千问-Turbo',
        description: '快速响应，适合日常对话',
        maxTokens: 8192
      },
      'qwen-plus': {
        name: '通义千问-Plus',
        description: '平衡性能和效果，适合复杂任务',
        maxTokens: 32768
      },
      'qwen-max': {
        name: '通义千问-Max',
        description: '最强性能，适合专业任务',
        maxTokens: 8192
      },
      'qwen-max-longcontext': {
        name: '通义千问-Max长文本',
        description: '支持长文本处理',
        maxTokens: 30000
      },
      'qwen2-72b-instruct': {
        name: '通义千问2-72B',
        description: '大参数模型，强大的理解和生成能力',
        maxTokens: 32768
      },
      'qwen2-7b-instruct': {
        name: '通义千问2-7B',
        description: '轻量级模型，快速响应',
        maxTokens: 32768
      }
    }

    return modelInfo[model] || {
      name: model,
      description: '阿里云通义千问模型',
      maxTokens: 8192
    }
  }

  /**
   * 估算token数量（简单估算）
   */
  static estimateTokens(text: string): number {
    // 简单估算：中文字符约1.5个token，英文单词约1个token
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length
    const otherChars = text.length - chineseChars - englishWords
    
    return Math.ceil(chineseChars * 1.5 + englishWords + otherChars * 0.5)
  }

  /**
   * 创建预设的阿里云模型服务实例
   */
  static createPresetService(modelType: 'turbo' | 'plus' | 'max' = 'turbo', apiKey: string): AliyunModelService {
    const modelMap = {
      turbo: 'qwen-turbo',
      plus: 'qwen-plus',
      max: 'qwen-max'
    }

    return new AliyunModelService({
      api_key: apiKey,
      model: modelMap[modelType],
      temperature: 0.7,
      max_tokens: 2048
    })
  }
}
