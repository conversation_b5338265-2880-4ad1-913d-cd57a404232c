import { pool } from '../config/database'
import { AliyunModelService } from './aliyunModelService'
import { MemoryService } from './memoryService'
import redis from '../utils/redis'

// 聊天消息接口
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

// 聊天响应接口
export interface EnhancedChatResponse {
  text: string
  emotion?: string
  personality_delta?: any
  memories_used: number
  memories_created: number
  context_length: number
  timestamp: number
  model_used: string
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

export class EnhancedChatService {
  /**
   * 处理增强对话请求
   */
  static async processChat(
    userId: number,
    deviceId: string,
    userInput: string
  ): Promise<EnhancedChatResponse> {
    try {
      // 1. 获取设备信息
      const device = await this.getDeviceInfo(deviceId)
      if (!device) {
        throw new Error(`设备不存在: ${deviceId}`)
      }

      // 2. 验证用户权限
      if (device.user_id !== userId) {
        throw new Error('无权访问此设备')
      }

      // 3. 获取角色信息
      const character = await this.getCharacterInfo(device.character_id)
      if (!character) {
        throw new Error('角色不存在')
      }

      // 4. 获取相关记忆
      const relevantMemories = await MemoryService.searchRelevantMemories(
        userId,
        device.character_id,
        userInput,
        5
      )

      // 5. 获取对话上下文
      const context = await this.getConversationContext(userId, deviceId, 10)

      // 6. 构建增强的对话消息
      const messages = this.buildEnhancedMessages(
        character,
        relevantMemories,
        context,
        userInput
      )

      // 7. 获取模型配置并调用
      const modelConfig = await this.getModelConfig(device.model_key)
      const aliyunService = new AliyunModelService({
        api_key: modelConfig.api_key,
        model: modelConfig.model,
        temperature: modelConfig.temperature,
        max_tokens: modelConfig.max_tokens
      })

      const aiResponse = await aliyunService.chat({ messages })

      // 8. 从对话中提取新记忆
      await MemoryService.extractMemoriesFromChat(
        userId,
        device.character_id,
        userInput,
        aiResponse.content
      )

      // 9. 保存对话历史
      await this.saveChatHistory(
        userId,
        deviceId,
        device.character_id,
        userInput,
        aiResponse.content
      )

      // 10. 更新对话上下文缓存
      await this.updateContextCache(userId, deviceId, userInput, aiResponse.content)

      return {
        text: aiResponse.content,
        emotion: this.analyzeEmotion(aiResponse.content),
        memories_used: relevantMemories.length,
        memories_created: 0, // 这里可以返回实际创建的记忆数量
        context_length: messages.length,
        timestamp: Date.now(),
        model_used: aiResponse.model,
        usage: aiResponse.usage
      }
    } catch (error) {
      console.error('增强对话处理失败:', error)
      throw error
    }
  }

  /**
   * 获取设备信息
   */
  private static async getDeviceInfo(deviceId: string): Promise<any> {
    const [rows]: any = await pool.execute(
      'SELECT * FROM devices WHERE device_id = ? AND status != "disabled"',
      [deviceId]
    )
    return rows.length > 0 ? rows[0] : null
  }

  /**
   * 获取角色信息
   */
  private static async getCharacterInfo(characterId: number): Promise<any> {
    const [rows]: any = await pool.execute(
      'SELECT * FROM characters WHERE id = ? AND status = 1',
      [characterId]
    )
    
    if (rows.length > 0) {
      const character = rows[0]
      character.personality_template = typeof character.personality_template === 'string'
        ? JSON.parse(character.personality_template)
        : character.personality_template
      return character
    }
    return null
  }

  /**
   * 获取模型配置
   */
  private static async getModelConfig(modelKey: string): Promise<any> {
    const [rows]: any = await pool.execute(
      'SELECT * FROM models WHERE model_key = ? AND status = 1',
      [modelKey]
    )
    
    if (rows.length > 0) {
      const model = rows[0]
      const config = typeof model.config === 'string' ? JSON.parse(model.config) : model.config
      return config
    }
    throw new Error(`模型配置不存在: ${modelKey}`)
  }

  /**
   * 获取对话上下文
   */
  private static async getConversationContext(
    userId: number,
    deviceId: string,
    limit: number = 10
  ): Promise<ChatMessage[]> {
    // 先尝试从缓存获取
    const cacheKey = `context:${userId}:${deviceId}`
    const cached = await redis.get(cacheKey)
    
    if (cached) {
      return JSON.parse(cached)
    }

    // 从数据库获取
    const [rows]: any = await pool.execute(
      `SELECT user_input, ai_response FROM chat_histories 
       WHERE user_id = ? AND device_id = ? 
       ORDER BY created_at DESC 
       LIMIT ?`,
      [userId, deviceId, limit]
    )

    const context: ChatMessage[] = []
    rows.reverse().forEach((row: any) => {
      context.push(
        { role: 'user', content: row.user_input },
        { role: 'assistant', content: row.ai_response }
      )
    })

    // 缓存结果
    await redis.setex(cacheKey, 1800, JSON.stringify(context)) // 30分钟缓存

    return context
  }

  /**
   * 构建增强的对话消息
   */
  private static buildEnhancedMessages(
    character: any,
    memories: any[],
    context: ChatMessage[],
    userInput: string
  ): ChatMessage[] {
    const messages: ChatMessage[] = []

    // 系统提示词
    let systemPrompt = character.prompt_template

    // 添加性格信息
    const personalityStr = Object.entries(character.personality_template)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ')
    
    systemPrompt += `\n\n角色性格: ${personalityStr}`

    // 添加记忆信息
    if (memories.length > 0) {
      systemPrompt += '\n\n关于用户的记忆:'
      memories.forEach((memory, index) => {
        systemPrompt += `\n${index + 1}. ${memory.content} (重要性: ${memory.importance})`
      })
      systemPrompt += '\n\n请根据这些记忆信息来个性化你的回复。'
    }

    messages.push({ role: 'system', content: systemPrompt })

    // 添加对话上下文
    messages.push(...context)

    // 添加当前用户输入
    messages.push({ role: 'user', content: userInput })

    return messages
  }

  /**
   * 保存对话历史
   */
  private static async saveChatHistory(
    userId: number,
    deviceId: string,
    characterId: number,
    userInput: string,
    aiResponse: string
  ): Promise<void> {
    await pool.execute(
      `INSERT INTO chat_histories (user_id, device_id, character_id, user_input, ai_response, created_at) 
       VALUES (?, ?, ?, ?, ?, NOW())`,
      [userId, deviceId, characterId, userInput, aiResponse]
    )
  }

  /**
   * 更新上下文缓存
   */
  private static async updateContextCache(
    userId: number,
    deviceId: string,
    userInput: string,
    aiResponse: string
  ): Promise<void> {
    const cacheKey = `context:${userId}:${deviceId}`
    
    try {
      let context = await this.getConversationContext(userId, deviceId, 10)
      
      // 添加新的对话
      context.push(
        { role: 'user', content: userInput },
        { role: 'assistant', content: aiResponse }
      )

      // 限制上下文长度
      if (context.length > 20) {
        context = context.slice(-20)
      }

      // 更新缓存
      await redis.setex(cacheKey, 1800, JSON.stringify(context))
    } catch (error) {
      console.error('更新上下文缓存失败:', error)
    }
  }

  /**
   * 简单的情感分析
   */
  private static analyzeEmotion(text: string): string {
    const emotions = {
      happy: ['开心', '高兴', '快乐', '哈哈', '😊', '😄', '😃'],
      sad: ['难过', '伤心', '悲伤', '😢', '😭', '😞'],
      angry: ['生气', '愤怒', '气愤', '😠', '😡'],
      excited: ['兴奋', '激动', '太棒了', '😆', '🎉'],
      calm: ['平静', '冷静', '淡定', '😌'],
      surprised: ['惊讶', '震惊', '没想到', '😲', '😮']
    }

    for (const [emotion, keywords] of Object.entries(emotions)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        return emotion
      }
    }

    return 'neutral'
  }

  /**
   * 获取对话历史
   */
  static async getChatHistory(
    userId: number,
    deviceId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{ items: any[]; total: number }> {
    try {
      // 计算总数
      const [countResult]: any = await pool.execute(
        'SELECT COUNT(*) as total FROM chat_histories WHERE user_id = ? AND device_id = ?',
        [userId, deviceId]
      )
      const total = countResult[0].total

      // 分页查询
      const offset = (page - 1) * limit
      const [rows]: any = await pool.execute(
        `SELECT ch.*, c.name as character_name 
         FROM chat_histories ch 
         LEFT JOIN characters c ON ch.character_id = c.id 
         WHERE ch.user_id = ? AND ch.device_id = ? 
         ORDER BY ch.created_at DESC 
         LIMIT ? OFFSET ?`,
        [userId, deviceId, limit, offset]
      )

      return { items: rows, total }
    } catch (error) {
      console.error('获取对话历史失败:', error)
      return { items: [], total: 0 }
    }
  }

  /**
   * 清除对话上下文
   */
  static async clearContext(userId: number, deviceId: string): Promise<void> {
    const cacheKey = `context:${userId}:${deviceId}`
    await redis.del(cacheKey)
  }
}
