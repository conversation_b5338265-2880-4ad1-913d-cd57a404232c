import mqtt, { MqttClient } from 'mqtt'
import { pool } from '../config/database'
import { EnhancedChatService } from './enhancedChatService'

// MQTT消息接口
interface XstarCallbackMessage {
  device_id: string
  user_input: string
  xstar_id: string
  timestamp: number
}

interface XstarHeartbeatMessage {
  device_id: string
  xstar_id: string
  battery_level: number
  wifi_strength: number
  timestamp: number
}

interface AIResponseMessage {
  device_id: string
  response_text: string
  screen?: {
    image_url?: string
    image_urls?: string[]
    image_timer?: number
    image_counter?: number
    image_mode?: 'once' | 'loop'
  }
  servos?: {
    left_ear?: number[]
    right_ear?: number[]
    tail?: number[]
  }
  timestamp: number
}

interface ErrorMessage {
  error: string
  message: string
  code: number
  timestamp: string
}

export class ExternalMqttService {
  private client: MqttClient | null = null
  private isConnected: boolean = false
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 10

  constructor() {
    this.connect()
  }

  /**
   * 记录MQTT消息日志
   */
  private async logMqttMessage(
    direction: 'received' | 'sent',
    topic: string,
    message: any,
    deviceId?: string,
    xstarId?: string
  ): Promise<void> {
    try {
      const logData = {
        direction,
        topic,
        message: typeof message === 'string' ? message : JSON.stringify(message),
        message_size: Buffer.byteLength(typeof message === 'string' ? message : JSON.stringify(message)),
        device_id: deviceId || null,
        xstar_id: xstarId || null,
        timestamp: new Date(),
        success: true
      }

      // 控制台输出详细日志
      const logPrefix = direction === 'received' ? '📥 [MQTT收到]' : '📤 [MQTT发送]'
      const deviceInfo = deviceId ? ` [设备:${deviceId}]` : ''
      const xstarInfo = xstarId ? ` [厂商:${xstarId}]` : ''

      console.log(`${logPrefix}${deviceInfo}${xstarInfo}`)
      console.log(`  主题: ${topic}`)
      console.log(`  大小: ${logData.message_size} bytes`)
      console.log(`  时间: ${logData.timestamp.toISOString()}`)
      console.log(`  内容: ${logData.message}`)
      console.log('─'.repeat(80))

      // 存储到数据库
      await pool.execute(
        `INSERT INTO mqtt_logs (direction, topic, message, message_size, device_id, xstar_id, timestamp, success)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          logData.direction,
          logData.topic,
          logData.message,
          logData.message_size,
          logData.device_id,
          logData.xstar_id,
          logData.timestamp,
          logData.success
        ]
      )
    } catch (error) {
      console.error('记录MQTT日志失败:', error)
    }
  }

  /**
   * 记录MQTT错误日志
   */
  private async logMqttError(
    direction: 'received' | 'sent',
    topic: string,
    message: any,
    error: Error,
    deviceId?: string,
    xstarId?: string
  ): Promise<void> {
    try {
      const logData = {
        direction,
        topic,
        message: typeof message === 'string' ? message : JSON.stringify(message),
        message_size: Buffer.byteLength(typeof message === 'string' ? message : JSON.stringify(message)),
        device_id: deviceId || null,
        xstar_id: xstarId || null,
        timestamp: new Date(),
        success: false,
        error_message: error.message
      }

      // 控制台输出错误日志
      const logPrefix = direction === 'received' ? '❌ [MQTT接收错误]' : '❌ [MQTT发送错误]'
      const deviceInfo = deviceId ? ` [设备:${deviceId}]` : ''
      const xstarInfo = xstarId ? ` [厂商:${xstarId}]` : ''

      console.error(`${logPrefix}${deviceInfo}${xstarInfo}`)
      console.error(`  主题: ${topic}`)
      console.error(`  错误: ${error.message}`)
      console.error(`  内容: ${logData.message}`)
      console.error('─'.repeat(80))

      // 存储到数据库
      await pool.execute(
        `INSERT INTO mqtt_logs (direction, topic, message, message_size, device_id, xstar_id, timestamp, success, error_message)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          logData.direction,
          logData.topic,
          logData.message,
          logData.message_size,
          logData.device_id,
          logData.xstar_id,
          logData.timestamp,
          logData.success,
          logData.error_message
        ]
      )
    } catch (logError) {
      console.error('记录MQTT错误日志失败:', logError)
    }
  }

  /**
   * 连接到外部MQTT服务器
   */
  private connect(): void {
    try {
      const options = {
        host: process.env.MQTT_XSTAR_HOST || '**************',
        port: parseInt(process.env.MQTT_XSTAR_PORT || '10079'),
        protocol: 'mqtt' as const,
        username: process.env.MQTT_XSTAR_USERNAME || 'xstar_platform',
        password: process.env.MQTT_XSTAR_PASSWORD || 'your_api_key',
        clientId: process.env.MQTT_XSTAR_CLIENT_ID || `platform_${Date.now()}`,
        clean: true,
        keepalive: 60,
        reconnectPeriod: 5000,
        connectTimeout: 30000
      }

      console.log('连接外部MQTT服务器...', options.host + ':' + options.port)
      
      this.client = mqtt.connect(options)

      this.client.on('connect', () => {
        console.log('外部MQTT连接成功')
        this.isConnected = true
        this.reconnectAttempts = 0
        this.subscribeToTopics()
      })

      this.client.on('message', (topic, message) => {
        this.handleMessage(topic, message)
      })

      this.client.on('error', (error) => {
        console.error('外部MQTT连接错误:', error)
        this.isConnected = false
      })

      this.client.on('close', () => {
        console.log('外部MQTT连接关闭')
        this.isConnected = false
      })

      this.client.on('reconnect', () => {
        this.reconnectAttempts++
        console.log(`外部MQTT重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`)
        
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          console.error('外部MQTT重连失败，达到最大重试次数')
          this.client?.end()
        }
      })

    } catch (error) {
      console.error('外部MQTT连接初始化失败:', error)
    }
  }

  /**
   * 订阅主题
   */
  private subscribeToTopics(): void {
    if (!this.client || !this.isConnected) return

    const topics = [
      'xstar/callback',
      'xstar/heartbeat'
    ]

    topics.forEach(topic => {
      this.client!.subscribe(topic, { qos: 1 }, (error) => {
        if (error) {
          console.error(`订阅主题失败 ${topic}:`, error)
        } else {
          console.log(`成功订阅主题: ${topic}`)
        }
      })
    })
  }

  /**
   * 处理接收到的消息
   */
  private async handleMessage(topic: string, message: Buffer): Promise<void> {
    const messageStr = message.toString()
    let parsedMessage: any = null
    let deviceId: string | undefined
    let xstarId: string | undefined

    try {
      parsedMessage = JSON.parse(messageStr)
      deviceId = parsedMessage.device_id
      xstarId = parsedMessage.xstar_id

      // 记录接收到的消息日志
      await this.logMqttMessage('received', topic, parsedMessage, deviceId, xstarId)

      switch (topic) {
        case 'xstar/callback':
          await this.handleCallbackMessage(parsedMessage)
          break
        case 'xstar/heartbeat':
          await this.handleHeartbeatMessage(parsedMessage)
          break
        default:
          console.warn('未知主题:', topic)
          await this.logMqttError('received', topic, messageStr, new Error('未知主题'), deviceId, xstarId)
      }
    } catch (error) {
      console.error('处理MQTT消息失败:', error)
      await this.logMqttError('received', topic, messageStr, error as Error, deviceId, xstarId)
    }
  }

  /**
   * 处理对话回调消息
   */
  private async handleCallbackMessage(message: XstarCallbackMessage): Promise<void> {
    try {
      const { device_id, user_input, xstar_id, timestamp } = message

      // 验证设备是否存在
      const device = await this.getDeviceInfo(device_id)
      if (!device) {
        await this.sendErrorMessage(device_id, '设备不存在', 404)
        return
      }

      // 验证厂商权限
      if (!await this.validateXstarAccess(xstar_id, device_id)) {
        await this.sendErrorMessage(device_id, '厂商无权访问此设备', 403)
        return
      }

      // 处理对话
      const chatResponse = await EnhancedChatService.processChat(
        device.user_id,
        device_id,
        user_input
      )

      // 生成硬件控制指令
      const hardwareControl = await this.generateHardwareControl(
        chatResponse.text,
        chatResponse.emotion || 'neutral'
      )

      // 发送AI回复
      const responseMessage: AIResponseMessage = {
        device_id,
        response_text: chatResponse.text,
        screen: hardwareControl.screen,
        servos: hardwareControl.servos,
        timestamp: Date.now()
      }

      console.log(`🤖 [AI回复生成] 设备:${device_id}`)
      console.log(`  用户输入: ${user_input}`)
      console.log(`  AI回复: ${chatResponse.text}`)
      console.log(`  情感: ${chatResponse.emotion || 'neutral'}`)
      console.log(`  硬件控制: ${JSON.stringify(hardwareControl)}`)

      await this.publishResponse(device_id, responseMessage)

      // 更新设备状态
      await this.updateDeviceStatus(device_id, 'online')

    } catch (error) {
      console.error('处理对话回调失败:', error)
      await this.logMqttError('received', 'xstar/callback', message, error as Error, message.device_id, message.xstar_id)
      await this.sendErrorMessage(message.device_id, '处理失败', 500)
    }
  }

  /**
   * 处理心跳消息
   */
  private async handleHeartbeatMessage(message: XstarHeartbeatMessage): Promise<void> {
    try {
      const { device_id, xstar_id, battery_level, wifi_strength, timestamp } = message

      // 验证设备和厂商
      const device = await this.getDeviceInfo(device_id)
      if (!device) {
        return // 心跳消息不发送错误响应
      }

      if (!await this.validateXstarAccess(xstar_id, device_id)) {
        return
      }

      // 更新设备状态
      await this.updateDeviceHeartbeat(device_id, {
        battery_level,
        wifi_strength,
        last_heartbeat: new Date()
      })

      console.log(`💓 [心跳处理] 设备:${device_id}`)
      console.log(`  电池电量: ${battery_level}%`)
      console.log(`  WiFi信号: ${(wifi_strength * 100).toFixed(1)}%`)
      console.log(`  时间戳: ${new Date(timestamp).toISOString()}`)

      // 发送心跳确认
      const ackMessage = {
        status: 'ok',
        server_time: new Date().toISOString(),
        device_id
      }

      await this.publishHeartbeatAck(device_id, ackMessage)

    } catch (error) {
      console.error('处理心跳消息失败:', error)
      await this.logMqttError('received', 'xstar/heartbeat', message, error as Error, message.device_id, message.xstar_id)
    }
  }

  /**
   * 获取设备信息
   */
  private async getDeviceInfo(deviceId: string): Promise<any> {
    try {
      const [rows]: any = await pool.execute(
        'SELECT * FROM devices WHERE device_id = ? AND status != "disabled"',
        [deviceId]
      )
      return rows.length > 0 ? rows[0] : null
    } catch (error) {
      console.error('获取设备信息失败:', error)
      return null
    }
  }

  /**
   * 验证厂商访问权限
   */
  private async validateXstarAccess(xstarId: string, deviceId: string): Promise<boolean> {
    try {
      // 这里可以实现更复杂的厂商权限验证逻辑
      // 目前简单验证设备是否存在
      const device = await this.getDeviceInfo(deviceId)
      return !!device
    } catch (error) {
      console.error('验证厂商权限失败:', error)
      return false
    }
  }

  /**
   * 生成硬件控制指令
   */
  private async generateHardwareControl(responseText: string, emotion: string): Promise<{
    screen?: any
    servos?: any
  }> {
    // 根据情感和回复内容生成硬件控制指令
    const control: any = {}

    // 屏幕控制
    control.screen = {
      image_url: this.getEmotionImageUrl(emotion),
      image_timer: 100,
      image_counter: 30,
      image_mode: 'once'
    }

    // 舵机控制
    control.servos = this.getEmotionServos(emotion)

    return control
  }

  /**
   * 根据情感获取图片URL
   */
  private getEmotionImageUrl(emotion: string): string {
    const emotionImages = {
      happy: 'https://assets.ai-doll.cn/emotions/happy.gif',
      sad: 'https://assets.ai-doll.cn/emotions/sad.gif',
      angry: 'https://assets.ai-doll.cn/emotions/angry.gif',
      excited: 'https://assets.ai-doll.cn/emotions/excited.gif',
      calm: 'https://assets.ai-doll.cn/emotions/calm.gif',
      surprised: 'https://assets.ai-doll.cn/emotions/surprised.gif',
      neutral: 'https://assets.ai-doll.cn/emotions/neutral.gif'
    }
    return emotionImages[emotion as keyof typeof emotionImages] || emotionImages.neutral
  }

  /**
   * 根据情感获取舵机动作
   */
  private getEmotionServos(emotion: string): any {
    const emotionServos = {
      happy: {
        left_ear: [1, 0, 1, 0],
        right_ear: [1, 0, 1, 0],
        tail: [1, 1, 1, 1]
      },
      sad: {
        left_ear: [-1, 0, -1, 0],
        right_ear: [-1, 0, -1, 0],
        tail: [0, 0, 0, 0]
      },
      excited: {
        left_ear: [1, -1, 1, -1],
        right_ear: [1, -1, 1, -1],
        tail: [1, -1, 1, -1]
      },
      neutral: {
        left_ear: [0, 0, 0, 0],
        right_ear: [0, 0, 0, 0],
        tail: [0, 0, 0, 0]
      }
    }
    return emotionServos[emotion as keyof typeof emotionServos] || emotionServos.neutral
  }

  /**
   * 发送AI回复
   */
  private async publishResponse(deviceId: string, message: AIResponseMessage): Promise<void> {
    if (!this.client || !this.isConnected) {
      throw new Error('MQTT客户端未连接')
    }

    const topic = `xstar/${deviceId}/response`
    const payload = JSON.stringify(message)

    return new Promise(async (resolve, reject) => {
      this.client!.publish(topic, payload, { qos: 1 }, async (error) => {
        if (error) {
          console.error('发送AI回复失败:', error)
          await this.logMqttError('sent', topic, message, error, deviceId)
          reject(error)
        } else {
          await this.logMqttMessage('sent', topic, message, deviceId)
          resolve()
        }
      })
    })
  }

  /**
   * 发送心跳确认
   */
  private async publishHeartbeatAck(deviceId: string, message: any): Promise<void> {
    if (!this.client || !this.isConnected) return

    const topic = `xstar/${deviceId}/heartbeat_ack`
    const payload = JSON.stringify(message)

    this.client.publish(topic, payload, { qos: 0 }, async (error) => {
      if (error) {
        console.error('发送心跳确认失败:', error)
        await this.logMqttError('sent', topic, message, error, deviceId)
      } else {
        await this.logMqttMessage('sent', topic, message, deviceId)
      }
    })
  }

  /**
   * 发送错误消息
   */
  private async sendErrorMessage(deviceId: string, message: string, code: number): Promise<void> {
    if (!this.client || !this.isConnected) return

    const topic = `xstar/${deviceId}/error`
    const errorMessage: ErrorMessage = {
      error: '处理失败',
      message,
      code,
      timestamp: new Date().toISOString()
    }

    this.client.publish(topic, JSON.stringify(errorMessage), { qos: 1 }, async (error) => {
      if (error) {
        console.error('发送错误消息失败:', error)
        await this.logMqttError('sent', topic, errorMessage, error, deviceId)
      } else {
        await this.logMqttMessage('sent', topic, errorMessage, deviceId)
      }
    })
  }

  /**
   * 更新设备状态
   */
  private async updateDeviceStatus(deviceId: string, status: string): Promise<void> {
    try {
      await pool.execute(
        'UPDATE devices SET status = ?, last_heartbeat = NOW() WHERE device_id = ?',
        [status, deviceId]
      )
    } catch (error) {
      console.error('更新设备状态失败:', error)
    }
  }

  /**
   * 更新设备心跳信息
   */
  private async updateDeviceHeartbeat(deviceId: string, data: {
    battery_level: number
    wifi_strength: number
    last_heartbeat: Date
  }): Promise<void> {
    try {
      await pool.execute(
        `UPDATE devices SET 
         status = 'online', 
         last_heartbeat = ?, 
         cooldown_policy = JSON_SET(
           COALESCE(cooldown_policy, '{}'), 
           '$.battery_level', ?, 
           '$.wifi_strength', ?
         )
         WHERE device_id = ?`,
        [data.last_heartbeat, data.battery_level, data.wifi_strength, deviceId]
      )
    } catch (error) {
      console.error('更新设备心跳信息失败:', error)
    }
  }

  /**
   * 获取连接状态
   */
  public getConnectionStatus(): { connected: boolean; reconnectAttempts: number } {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts
    }
  }

  /**
   * 断开连接
   */
  public disconnect(): void {
    if (this.client) {
      this.client.end()
      this.client = null
      this.isConnected = false
    }
  }
}
