import axios from 'axios'
import AIModel from '../models/model'

// 模型类型枚举
export enum ModelType {
  LOCAL = 'local',
  CLOUD = 'cloud'
}

// 模型调用接口
export interface ModelCallRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant'
    content: string
  }>
  temperature?: number
  max_tokens?: number
  stream?: boolean
}

// 模型调用响应
export interface ModelCallResponse {
  content: string
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
  model: string
}

// 模型调用错误
export class ModelCallError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public modelKey: string = ''
  ) {
    super(message)
    this.name = 'ModelCallError'
  }
}

export class ModelService {
  /**
   * 调用模型生成回复
   */
  static async callModel(
    modelKey: string,
    request: ModelCallRequest
  ): Promise<ModelCallResponse> {
    try {
      // 获取模型配置
      const model = await AIModel.findOne({ where: { model_key: modelKey } })
      if (!model) {
        throw new ModelCallError(`模型 ${modelKey} 不存在`, 404, modelKey)
      }

      if (model.status !== 1) {
        throw new ModelCallError(`模型 ${modelKey} 已禁用`, 400, modelKey)
      }

      // 根据模型类型调用不同的处理方法
      if (model.type === ModelType.CLOUD) {
        return await this.callCloudModel(model, request)
      } else {
        return await this.callLocalModel(model, request)
      }
    } catch (error) {
      if (error instanceof ModelCallError) {
        throw error
      }
      throw new ModelCallError(
        `调用模型失败: ${error instanceof Error ? error.message : '未知错误'}`,
        500,
        modelKey
      )
    }
  }

  /**
   * 调用云端模型
   */
  private static async callCloudModel(
    model: any,
    request: ModelCallRequest
  ): Promise<ModelCallResponse> {
    const config = model.config
    const provider = config.provider

    switch (provider) {
      case 'openai':
        return await this.callOpenAI(config, request)
      case 'anthropic':
        return await this.callAnthropic(config, request)
      case 'aliyun':
        return await this.callAliyun(config, request)
      default:
        throw new ModelCallError(`不支持的云端模型提供商: ${provider}`, 400, model.model_key)
    }
  }

  /**
   * 调用OpenAI模型
   */
  private static async callOpenAI(
    config: any,
    request: ModelCallRequest
  ): Promise<ModelCallResponse> {
    const response = await axios.post(
      config.api_url,
      {
        model: config.model || 'gpt-3.5-turbo',
        messages: request.messages,
        temperature: request.temperature || config.temperature || 0.7,
        max_tokens: request.max_tokens || config.max_tokens || 2048,
        stream: request.stream || false
      },
      {
        headers: {
          'Authorization': `Bearer ${config.api_key}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    )

    const data = response.data
    return {
      content: data.choices[0].message.content,
      usage: data.usage,
      model: data.model
    }
  }

  /**
   * 调用Anthropic模型
   */
  private static async callAnthropic(
    config: any,
    request: ModelCallRequest
  ): Promise<ModelCallResponse> {
    const response = await axios.post(
      config.api_url,
      {
        model: config.model || 'claude-3-opus-20240229',
        messages: request.messages,
        max_tokens: request.max_tokens || config.max_tokens || 4096,
        temperature: request.temperature || config.temperature || 0.7
      },
      {
        headers: {
          'x-api-key': config.api_key,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        },
        timeout: 30000
      }
    )

    const data = response.data
    return {
      content: data.content[0].text,
      usage: data.usage,
      model: data.model
    }
  }

  /**
   * 调用阿里云模型 (OpenAI兼容接口)
   */
  private static async callAliyun(
    config: any,
    request: ModelCallRequest
  ): Promise<ModelCallResponse> {
    // 阿里云通义千问现在支持OpenAI兼容接口
    const apiUrl = config.api_url || 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'

    const response = await axios.post(
      apiUrl,
      {
        model: config.model || 'qwen-turbo',
        messages: request.messages,
        temperature: request.temperature || config.temperature || 0.7,
        max_tokens: request.max_tokens || config.max_tokens || 2048,
        stream: request.stream || false
      },
      {
        headers: {
          'Authorization': `Bearer ${config.api_key}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    )

    const data = response.data
    return {
      content: data.choices[0].message.content,
      usage: data.usage,
      model: data.model
    }
  }

  /**
   * 调用本地模型
   */
  private static async callLocalModel(
    model: any,
    request: ModelCallRequest
  ): Promise<ModelCallResponse> {
    const config = model.config
    const provider = config.provider

    switch (provider) {
      case 'zhipu':
        return await this.callZhipuLocal(config, request)
      default:
        throw new ModelCallError(`不支持的本地模型提供商: ${provider}`, 400, model.model_key)
    }
  }

  /**
   * 调用智谱本地模型
   */
  private static async callZhipuLocal(
    config: any,
    request: ModelCallRequest
  ): Promise<ModelCallResponse> {
    const response = await axios.post(
      config.api_url || 'http://localhost:8000/v1/chat/completions',
      {
        model: config.model || 'chatglm3-6b',
        messages: request.messages,
        temperature: request.temperature || config.temperature || 0.7,
        max_tokens: request.max_tokens || config.max_tokens || 2048
      },
      {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 60000 // 本地模型可能需要更长时间
      }
    )

    const data = response.data
    return {
      content: data.choices[0].message.content,
      usage: data.usage,
      model: data.model
    }
  }

  /**
   * 获取模型列表
   */
  static async getAvailableModels(): Promise<any[]> {
    try {
      const models = await AIModel.findAll({
        where: { status: 1 },
        attributes: ['model_key', 'name', 'type', 'config']
      })
      return models.map(model => ({
        model_key: model.model_key,
        name: model.name,
        type: model.type,
        provider: model.config?.provider || 'unknown'
      }))
    } catch (error) {
      console.error('获取可用模型列表失败:', error)
      return []
    }
  }

  /**
   * 检查模型健康状态
   */
  static async checkModelHealth(modelKey: string): Promise<boolean> {
    try {
      const model = await AIModel.findOne({ where: { model_key: modelKey } })
      if (!model || model.status !== 1) {
        return false
      }

      // 简单的健康检查请求
      const testRequest: ModelCallRequest = {
        messages: [
          { role: 'user', content: '你好' }
        ],
        max_tokens: 10
      }

      await this.callModel(modelKey, testRequest)
      return true
    } catch (error) {
      console.error(`模型 ${modelKey} 健康检查失败:`, error)
      return false
    }
  }
} 
