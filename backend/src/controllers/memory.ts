import { Request, Response } from 'express'
import { MemoryService, MemoryType } from '../services/memoryService'

interface AuthRequest extends Request {
  user?: {
    id: number
    username: string
    tenant_id: number | null
    role: string
  }
}

// 获取记忆列表
export const getMemories = async (req: AuthRequest, res: Response) => {
  try {
    const { character_id, memory_type, limit = 50 } = req.query
    const userId = req.user!.id

    if (!character_id) {
      return res.status(400).json({
        code: 400,
        message: '角色ID不能为空'
      })
    }

    const memories = await MemoryService.getMemories(
      userId,
      Number(character_id),
      memory_type as MemoryType,
      Number(limit)
    )

    res.json({
      code: 0,
      data: memories
    })
  } catch (error) {
    console.error('获取记忆列表失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 添加记忆
export const addMemory = async (req: AuthRequest, res: Response) => {
  try {
    const { character_id, memory_type, content, importance = 5 } = req.body
    const userId = req.user!.id

    if (!character_id || !memory_type || !content) {
      return res.status(400).json({
        code: 400,
        message: '角色ID、记忆类型和内容不能为空'
      })
    }

    // 验证记忆类型
    if (!Object.values(MemoryType).includes(memory_type)) {
      return res.status(400).json({
        code: 400,
        message: '无效的记忆类型'
      })
    }

    // 验证重要性范围
    if (importance < 1 || importance > 10) {
      return res.status(400).json({
        code: 400,
        message: '重要性必须在1-10之间'
      })
    }

    const memoryId = await MemoryService.addMemory(
      userId,
      character_id,
      memory_type,
      content,
      importance
    )

    res.json({
      code: 0,
      message: '记忆添加成功',
      data: { id: memoryId }
    })
  } catch (error) {
    console.error('添加记忆失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 搜索相关记忆
export const searchMemories = async (req: AuthRequest, res: Response) => {
  try {
    const { character_id, query, limit = 10 } = req.query
    const userId = req.user!.id

    if (!character_id || !query) {
      return res.status(400).json({
        code: 400,
        message: '角色ID和搜索关键词不能为空'
      })
    }

    const memories = await MemoryService.searchRelevantMemories(
      userId,
      Number(character_id),
      String(query),
      Number(limit)
    )

    res.json({
      code: 0,
      data: memories
    })
  } catch (error) {
    console.error('搜索记忆失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 获取记忆统计
export const getMemoryStats = async (req: AuthRequest, res: Response) => {
  try {
    const { character_id } = req.query
    const userId = req.user!.id

    if (!character_id) {
      return res.status(400).json({
        code: 400,
        message: '角色ID不能为空'
      })
    }

    const stats = await MemoryService.getMemoryStats(userId, Number(character_id))

    res.json({
      code: 0,
      data: stats
    })
  } catch (error) {
    console.error('获取记忆统计失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 清理过期记忆
export const cleanupMemories = async (req: AuthRequest, res: Response) => {
  try {
    const { character_id } = req.body
    const userId = req.user!.id

    if (!character_id) {
      return res.status(400).json({
        code: 400,
        message: '角色ID不能为空'
      })
    }

    await MemoryService.cleanupOldMemories(userId, character_id)

    res.json({
      code: 0,
      message: '记忆清理完成'
    })
  } catch (error) {
    console.error('清理记忆失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 获取记忆类型列表
export const getMemoryTypes = async (req: Request, res: Response) => {
  try {
    const types = Object.values(MemoryType).map(type => ({
      value: type,
      label: getMemoryTypeLabel(type),
      description: getMemoryTypeDescription(type)
    }))

    res.json({
      code: 0,
      data: types
    })
  } catch (error) {
    console.error('获取记忆类型失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 批量导入记忆
export const importMemories = async (req: AuthRequest, res: Response) => {
  try {
    const { character_id, memories } = req.body
    const userId = req.user!.id

    if (!character_id || !Array.isArray(memories)) {
      return res.status(400).json({
        code: 400,
        message: '角色ID和记忆数组不能为空'
      })
    }

    const results = []
    for (const memory of memories) {
      try {
        const memoryId = await MemoryService.addMemory(
          userId,
          character_id,
          memory.memory_type,
          memory.content,
          memory.importance || 5
        )
        results.push({ success: true, id: memoryId, content: memory.content })
      } catch (error) {
        results.push({ success: false, error: (error as Error).message, content: memory.content })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failCount = results.length - successCount

    res.json({
      code: 0,
      message: `导入完成，成功${successCount}条，失败${failCount}条`,
      data: {
        total: results.length,
        success: successCount,
        failed: failCount,
        results
      }
    })
  } catch (error) {
    console.error('批量导入记忆失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 辅助函数：获取记忆类型标签
function getMemoryTypeLabel(type: MemoryType): string {
  const labels = {
    [MemoryType.PREFERENCE]: '偏好',
    [MemoryType.FACT]: '事实',
    [MemoryType.EMOTION]: '情感',
    [MemoryType.HABIT]: '习惯'
  }
  return labels[type] || type
}

// 辅助函数：获取记忆类型描述
function getMemoryTypeDescription(type: MemoryType): string {
  const descriptions = {
    [MemoryType.PREFERENCE]: '用户的喜好和偏好',
    [MemoryType.FACT]: '关于用户的客观事实',
    [MemoryType.EMOTION]: '用户的情感状态和感受',
    [MemoryType.HABIT]: '用户的行为习惯和模式'
  }
  return descriptions[type] || '自定义记忆类型'
}
