import { Request, Response } from 'express'
import { EnhancedChatService } from '../services/enhancedChatService'

interface AuthRequest extends Request {
  user?: {
    id: number
    username: string
    tenant_id: number | null
    role: string
  }
}

// 发送消息
export const sendMessage = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id, message } = req.body
    const userId = req.user!.id

    if (!device_id || !message) {
      return res.status(400).json({
        code: 400,
        message: '设备ID和消息内容不能为空'
      })
    }

    if (message.trim().length === 0) {
      return res.status(400).json({
        code: 400,
        message: '消息内容不能为空'
      })
    }

    if (message.length > 1000) {
      return res.status(400).json({
        code: 400,
        message: '消息内容过长，请控制在1000字符以内'
      })
    }

    const response = await EnhancedChatService.processChat(userId, device_id, message)

    res.json({
      code: 0,
      data: response
    })
  } catch (error: any) {
    console.error('发送消息失败:', error)
    
    // 根据错误类型返回不同的错误码
    if (error.message.includes('设备不存在')) {
      return res.status(404).json({
        code: 404,
        message: error.message
      })
    }
    
    if (error.message.includes('无权访问')) {
      return res.status(403).json({
        code: 403,
        message: error.message
      })
    }

    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 获取对话历史
export const getChatHistory = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id } = req.params
    const { page = 1, limit = 20 } = req.query
    const userId = req.user!.id

    if (!device_id) {
      return res.status(400).json({
        code: 400,
        message: '设备ID不能为空'
      })
    }

    const result = await EnhancedChatService.getChatHistory(
      userId,
      device_id,
      Number(page),
      Number(limit)
    )

    res.json({
      code: 0,
      data: {
        items: result.items,
        total: result.total,
        page: Number(page),
        limit: Number(limit)
      }
    })
  } catch (error) {
    console.error('获取对话历史失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 清除对话上下文
export const clearContext = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id } = req.body
    const userId = req.user!.id

    if (!device_id) {
      return res.status(400).json({
        code: 400,
        message: '设备ID不能为空'
      })
    }

    await EnhancedChatService.clearContext(userId, device_id)

    res.json({
      code: 0,
      message: '对话上下文已清除'
    })
  } catch (error) {
    console.error('清除对话上下文失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 批量发送消息（用于测试）
export const batchSendMessages = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id, messages } = req.body
    const userId = req.user!.id

    if (!device_id || !Array.isArray(messages)) {
      return res.status(400).json({
        code: 400,
        message: '设备ID和消息数组不能为空'
      })
    }

    if (messages.length > 10) {
      return res.status(400).json({
        code: 400,
        message: '批量消息数量不能超过10条'
      })
    }

    const results = []
    for (const message of messages) {
      try {
        const response = await EnhancedChatService.processChat(userId, device_id, message)
        results.push({
          success: true,
          input: message,
          response: response.text,
          memories_used: response.memories_used,
          timestamp: response.timestamp
        })
      } catch (error: any) {
        results.push({
          success: false,
          input: message,
          error: error.message
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failCount = results.length - successCount

    res.json({
      code: 0,
      message: `批量发送完成，成功${successCount}条，失败${failCount}条`,
      data: {
        total: results.length,
        success: successCount,
        failed: failCount,
        results
      }
    })
  } catch (error) {
    console.error('批量发送消息失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 获取对话统计
export const getChatStats = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id } = req.params
    const userId = req.user!.id

    if (!device_id) {
      return res.status(400).json({
        code: 400,
        message: '设备ID不能为空'
      })
    }

    // 这里可以添加更多统计逻辑
    const result = await EnhancedChatService.getChatHistory(userId, device_id, 1, 1)
    
    res.json({
      code: 0,
      data: {
        total_messages: result.total,
        device_id
      }
    })
  } catch (error) {
    console.error('获取对话统计失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 导出对话历史
export const exportChatHistory = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id } = req.params
    const { format = 'json', start_date, end_date } = req.query
    const userId = req.user!.id

    if (!device_id) {
      return res.status(400).json({
        code: 400,
        message: '设备ID不能为空'
      })
    }

    // 获取所有对话历史（这里简化处理，实际应该分批处理大量数据）
    const result = await EnhancedChatService.getChatHistory(userId, device_id, 1, 10000)

    if (format === 'json') {
      res.json({
        code: 0,
        data: {
          device_id,
          export_time: new Date().toISOString(),
          total_messages: result.total,
          conversations: result.items
        }
      })
    } else if (format === 'txt') {
      let textContent = `对话历史导出\n设备ID: ${device_id}\n导出时间: ${new Date().toISOString()}\n总消息数: ${result.total}\n\n`
      
      result.items.forEach((item: any, index: number) => {
        textContent += `=== 对话 ${index + 1} ===\n`
        textContent += `时间: ${item.created_at}\n`
        textContent += `用户: ${item.user_input}\n`
        textContent += `AI: ${item.ai_response}\n\n`
      })

      res.setHeader('Content-Type', 'text/plain; charset=utf-8')
      res.setHeader('Content-Disposition', `attachment; filename="chat_history_${device_id}.txt"`)
      res.send(textContent)
    } else {
      res.status(400).json({
        code: 400,
        message: '不支持的导出格式'
      })
    }
  } catch (error) {
    console.error('导出对话历史失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}
