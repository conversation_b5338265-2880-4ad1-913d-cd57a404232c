import { Request, Response } from 'express';
import { pool } from '../config/database';

// 扩展Request类型以包含用户信息
interface AuthRequest extends Request {
  user?: {
    id: number;
    username: string;
    tenant_id: number | null;
    role: string;
  };
}

// 角色类型枚举
export enum CharacterType {
  PET = 'pet',
  ASSISTANT = 'assistant',
  COMPANION = 'companion',
  MENTOR = 'mentor',
  ENTERTAINMENT = 'entertainment'
}

// 获取角色列表
export const getCharacters = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      type = '',
      is_public,
      my_only = false
    } = req.query;

    const currentUser = req.user!;
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    // 如果只查看自己的角色
    if (my_only === 'true') {
      whereClause += ' AND user_id = ?';
      params.push(currentUser.id);
    } else {
      // 查看公开角色或自己创建的角色
      whereClause += ' AND (is_public = TRUE OR user_id = ?)';
      params.push(currentUser.id);
    }

    // 搜索条件
    if (search) {
      whereClause += ' AND (name LIKE ? OR description LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm);
    }

    // 类型过滤
    if (type) {
      whereClause += ' AND type = ?';
      params.push(type);
    }

    // 公开性过滤
    if (is_public !== undefined) {
      whereClause += ' AND is_public = ?';
      params.push(is_public === 'true');
    }

    // 计算总数
    const [countResult]: any = await pool.execute(
      `SELECT COUNT(*) as total FROM characters ${whereClause}`,
      params
    );
    const total = countResult[0].total;

    // 分页查询
    const offset = (Number(page) - 1) * Number(limit);
    const [rows]: any = await pool.execute(
      `SELECT c.*, u.username as creator_name
       FROM characters c
       LEFT JOIN users u ON c.user_id = u.id
       ${whereClause}
       ORDER BY c.created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, Number(limit), offset]
    );

    res.json({
      code: 0,
      data: {
        items: rows.map((row: any) => ({
          ...row,
          personality_template: typeof row.personality_template === 'string'
            ? JSON.parse(row.personality_template)
            : row.personality_template,
          is_owner: row.user_id === currentUser.id
        })),
        total,
        page: Number(page),
        limit: Number(limit)
      }
    });
  } catch (error) {
    console.error('Get characters error:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    });
  }
};

// 创建角色
export const createCharacter = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const {
      name,
      type,
      description,
      avatar_url,
      personality_template,
      prompt_template,
      is_public = false
    } = req.body;

    const currentUser = req.user!;

    // 验证必填字段
    if (!name || !type || !prompt_template) {
      res.status(400).json({
        code: 400,
        message: '角色名称、类型和Prompt模板不能为空'
      });
      return;
    }

    // 验证角色类型
    if (!Object.values(CharacterType).includes(type)) {
      res.status(400).json({
        code: 400,
        message: '无效的角色类型'
      });
      return;
    }

    // 检查角色名称是否已存在（同一用户下）
    const [existing]: any = await pool.execute(
      'SELECT id FROM characters WHERE name = ? AND user_id = ?',
      [name, currentUser.id]
    );

    if (existing.length > 0) {
      res.status(400).json({
        code: 400,
        message: '角色名称已存在'
      });
      return;
    }

    // 默认性格模板
    const defaultPersonality = {
      friendliness: 70,
      intelligence: 70,
      activeness: 70,
      curiosity: 70,
      humor: 50,
      empathy: 60
    };

    // 创建角色
    const [result]: any = await pool.execute(
      `INSERT INTO characters (name, type, description, avatar_url, personality_template,
       prompt_template, is_public, user_id, tenant_id, status)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)`,
      [
        name,
        type,
        description || '',
        avatar_url || '',
        JSON.stringify(personality_template || defaultPersonality),
        prompt_template,
        is_public,
        currentUser.id,
        currentUser.tenant_id
      ]
    );

    res.json({
      code: 0,
      message: '角色创建成功',
      data: {
        id: result.insertId,
        name,
        type,
        description,
        is_public
      }
    });
  } catch (error) {
    console.error('Create character error:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    });
  }
};

// 获取单个角色详情
export const getCharacterById = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const currentUser = req.user!;

    const [rows]: any = await pool.execute(
      `SELECT c.*, u.username as creator_name
       FROM characters c
       LEFT JOIN users u ON c.user_id = u.id
       WHERE c.id = ? AND (c.is_public = TRUE OR c.user_id = ?)`,
      [id, currentUser.id]
    );

    if (rows.length === 0) {
      res.status(404).json({
        code: 404,
        message: '角色不存在或无权访问'
      });
      return;
    }

    const character = rows[0];
    res.json({
      code: 0,
      data: {
        ...character,
        personality_template: typeof character.personality_template === 'string'
          ? JSON.parse(character.personality_template)
          : character.personality_template,
        is_owner: character.user_id === currentUser.id
      }
    });
  } catch (error) {
    console.error('Get character by id error:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    });
  }
};

// 更新角色
export const updateCharacter = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const {
      name,
      type,
      description,
      avatar_url,
      personality_template,
      prompt_template,
      is_public
    } = req.body;

    const currentUser = req.user!;

    // 检查角色是否存在且用户有权限修改
    const [existing]: any = await pool.execute(
      'SELECT * FROM characters WHERE id = ? AND user_id = ?',
      [id, currentUser.id]
    );

    if (existing.length === 0) {
      res.status(404).json({
        code: 404,
        message: '角色不存在或无权修改'
      });
      return;
    }

    const character = existing[0];

    // 如果修改了名称，检查是否与其他角色重名
    if (name && name !== character.name) {
      const [nameCheck]: any = await pool.execute(
        'SELECT id FROM characters WHERE name = ? AND user_id = ? AND id != ?',
        [name, currentUser.id, id]
      );

      if (nameCheck.length > 0) {
        res.status(400).json({
          code: 400,
          message: '角色名称已存在'
        });
        return;
      }
    }

    // 验证角色类型
    if (type && !Object.values(CharacterType).includes(type)) {
      res.status(400).json({
        code: 400,
        message: '无效的角色类型'
      });
      return;
    }

    // 更新角色
    await pool.execute(
      `UPDATE characters SET
       name = ?, type = ?, description = ?, avatar_url = ?,
       personality_template = ?, prompt_template = ?, is_public = ?,
       updated_at = NOW()
       WHERE id = ?`,
      [
        name || character.name,
        type || character.type,
        description !== undefined ? description : character.description,
        avatar_url !== undefined ? avatar_url : character.avatar_url,
        personality_template ? JSON.stringify(personality_template) : character.personality_template,
        prompt_template || character.prompt_template,
        is_public !== undefined ? is_public : character.is_public,
        id
      ]
    );

    res.json({
      code: 0,
      message: '角色更新成功'
    });
  } catch (error) {
    console.error('Update character error:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    });
  }
};

// 删除角色
export const deleteCharacter = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const currentUser = req.user!;

    // 检查角色是否存在且用户有权限删除
    const [existing]: any = await pool.execute(
      'SELECT * FROM characters WHERE id = ? AND user_id = ?',
      [id, currentUser.id]
    );

    if (existing.length === 0) {
      res.status(404).json({
        code: 404,
        message: '角色不存在或无权删除'
      });
      return;
    }

    // 检查是否有设备正在使用此角色
    const [devices]: any = await pool.execute(
      'SELECT COUNT(*) as count FROM devices WHERE character_id = ?',
      [id]
    );

    if (devices[0].count > 0) {
      res.status(400).json({
        code: 400,
        message: '该角色正在被设备使用，无法删除'
      });
      return;
    }

    // 删除角色
    await pool.execute('DELETE FROM characters WHERE id = ?', [id]);

    res.json({
      code: 0,
      message: '角色删除成功'
    });
  } catch (error) {
    console.error('Delete character error:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    });
  }
};

// 复制角色
export const cloneCharacter = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { name } = req.body;
    const currentUser = req.user!;

    // 检查原角色是否存在且可访问
    const [original]: any = await pool.execute(
      'SELECT * FROM characters WHERE id = ? AND (is_public = TRUE OR user_id = ?)',
      [id, currentUser.id]
    );

    if (original.length === 0) {
      res.status(404).json({
        code: 404,
        message: '角色不存在或无权访问'
      });
      return;
    }

    const originalCharacter = original[0];
    const newName = name || `${originalCharacter.name}_副本`;

    // 检查新名称是否已存在
    const [nameCheck]: any = await pool.execute(
      'SELECT id FROM characters WHERE name = ? AND user_id = ?',
      [newName, currentUser.id]
    );

    if (nameCheck.length > 0) {
      res.status(400).json({
        code: 400,
        message: '角色名称已存在'
      });
      return;
    }

    // 创建角色副本
    const [result]: any = await pool.execute(
      `INSERT INTO characters (name, type, description, avatar_url, personality_template,
       prompt_template, is_public, user_id, tenant_id, status)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)`,
      [
        newName,
        originalCharacter.type,
        originalCharacter.description,
        originalCharacter.avatar_url,
        originalCharacter.personality_template,
        originalCharacter.prompt_template,
        false, // 副本默认为私有
        currentUser.id,
        currentUser.tenant_id
      ]
    );

    res.json({
      code: 0,
      message: '角色复制成功',
      data: {
        id: result.insertId,
        name: newName
      }
    });
  } catch (error) {
    console.error('Clone character error:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    });
  }
};

// 获取角色类型列表
export const getCharacterTypes = async (req: Request, res: Response): Promise<void> => {
  try {
    const types = Object.values(CharacterType).map(type => ({
      value: type,
      label: getTypeLabel(type),
      description: getTypeDescription(type)
    }));

    res.json({
      code: 0,
      data: types
    });
  } catch (error) {
    console.error('Get character types error:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    });
  }
};

// 获取预设性格模板
export const getPersonalityTemplates = async (req: Request, res: Response): Promise<void> => {
  try {
    const templates = [
      {
        name: '友善助手',
        template: { friendliness: 90, intelligence: 80, activeness: 70, curiosity: 60, humor: 50, empathy: 85 }
      },
      {
        name: '活泼宠物',
        template: { friendliness: 95, intelligence: 60, activeness: 95, curiosity: 90, humor: 80, empathy: 70 }
      },
      {
        name: '智慧导师',
        template: { friendliness: 70, intelligence: 95, activeness: 60, curiosity: 80, humor: 40, empathy: 75 }
      },
      {
        name: '温柔陪伴',
        template: { friendliness: 85, intelligence: 70, activeness: 50, curiosity: 60, humor: 60, empathy: 95 }
      },
      {
        name: '幽默娱乐',
        template: { friendliness: 80, intelligence: 65, activeness: 85, curiosity: 75, humor: 95, empathy: 70 }
      }
    ];

    res.json({
      code: 0,
      data: templates
    });
  } catch (error) {
    console.error('Get personality templates error:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    });
  }
};

// 辅助函数：获取类型标签
function getTypeLabel(type: CharacterType): string {
  const labels = {
    [CharacterType.PET]: '宠物',
    [CharacterType.ASSISTANT]: '助手',
    [CharacterType.COMPANION]: '陪伴者',
    [CharacterType.MENTOR]: '导师',
    [CharacterType.ENTERTAINMENT]: '娱乐'
  };
  return labels[type] || type;
}

// 辅助函数：获取类型描述
function getTypeDescription(type: CharacterType): string {
  const descriptions = {
    [CharacterType.PET]: '可爱的虚拟宠物，陪伴用户玩耍',
    [CharacterType.ASSISTANT]: '专业的AI助手，帮助解决问题',
    [CharacterType.COMPANION]: '温暖的陪伴者，提供情感支持',
    [CharacterType.MENTOR]: '智慧的导师，提供指导和建议',
    [CharacterType.ENTERTAINMENT]: '有趣的娱乐角色，带来欢乐'
  };
  return descriptions[type] || '自定义角色类型';
}
        { tenant_id },
        { is_public: true }
      ];
    } else {
      // 如果没有指定租户，则只返回公开角色
      whereClause.is_public = true;
    }
    
    // 分页参数
    const pageNum = parseInt(page as string, 10);
    const pageSize = parseInt(limit as string, 10);
    const offset = (pageNum - 1) * pageSize;
    
    // 查询角色列表
    const { count, rows } = await Character.findAndCountAll({
      where: whereClause,
      limit: pageSize,
      offset,
      order: [['created_at', 'DESC']]
    });
    
    res.json({
      code: 0,
      message: '获取角色列表成功',
      data: {
        items: rows,
        total: count,
        page: pageNum,
        limit: pageSize
      }
    });
  } catch (error) {
    console.error('获取角色列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取角色列表失败'
    });
  }
};

// 获取单个角色
export const getCharacterById = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const character = await Character.findByPk(id);
    
    if (!character) {
      res.status(404).json({
        code: 404,
        message: '角色不存在'
      });
      return;
    }
    
    // 检查访问权限
    if (!character.is_public && character.tenant_id !== req.user?.tenant_id) {
      res.status(403).json({
        code: 403,
        message: '无权访问该角色'
      });
      return;
    }
    
    res.json({
      code: 0,
      message: '获取角色成功',
      data: character
    });
  } catch (error) {
    console.error('获取角色失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取角色失败'
    });
  }
};

// 获取默认头像URL
const getDefaultAvatarUrl = (type: CharacterType): string => {
  const avatarMap = {
    [CharacterType.PET]: 'https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/72x72/1f430.png',
    [CharacterType.ASSISTANT]: 'https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/72x72/1f916.png',
    [CharacterType.COMPANION]: 'https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/72x72/1f603.png',
    [CharacterType.MENTOR]: 'https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/72x72/1f9d1-200d-1f3eb.png',
    [CharacterType.ENTERTAINMENT]: 'https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/72x72/1f3a4.png'
  };
  return avatarMap[type] || 'https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/72x72/2753.png';
};

// 创建角色
export const createCharacter = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const {
      name,
      type,
      description,
      avatar_url,
      personality_template,
      prompt_template,
      is_public
    } = req.body;
    
    // 验证必填字段
    if (!name || !type || !description || !prompt_template) {
      res.status(400).json({
        code: 400,
        message: '缺少必要参数'
      });
      return;
    }
    
    // 验证角色类型
    if (!Object.values(CharacterType).includes(type as CharacterType)) {
      res.status(400).json({
        code: 400,
        message: '无效的角色类型'
      });
      return;
    }
    
    // 如果没有提供头像URL，使用默认头像
    const finalAvatarUrl = avatar_url || getDefaultAvatarUrl(type as CharacterType);
    
    // 创建角色
    const character = await Character.create({
      name,
      type: type as CharacterType,
      description,
      avatar_url: finalAvatarUrl,
      personality_template: personality_template || {},
      prompt_template,
      is_public: !!is_public,
      tenant_id: req.user?.tenant_id || null
    });
    
    res.status(201).json({
      code: 0,
      message: '创建角色成功',
      data: character
    });
  } catch (error) {
    console.error('创建角色失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建角色失败'
    });
  }
};

// 更新角色
export const updateCharacter = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const {
      name,
      type,
      description,
      avatar_url,
      personality_template,
      prompt_template,
      is_public
    } = req.body;
    
    // 查找角色
    const character = await Character.findByPk(id);
    
    if (!character) {
      res.status(404).json({
        code: 404,
        message: '角色不存在'
      });
      return;
    }
    
    // 检查更新权限
    if (character.tenant_id !== req.user?.tenant_id && req.user?.role !== 'admin') {
      res.status(403).json({
        code: 403,
        message: '无权更新该角色'
      });
      return;
    }
    
    // 验证角色类型
    const updatedType = type as CharacterType || character.type;
    if (type && !Object.values(CharacterType).includes(updatedType)) {
      res.status(400).json({
        code: 400,
        message: '无效的角色类型'
      });
      return;
    }
    
    // 如果没有提供头像URL，使用默认头像
    let finalAvatarUrl = avatar_url;
    if (!finalAvatarUrl && type && type !== character.type) {
      finalAvatarUrl = getDefaultAvatarUrl(updatedType);
    }
    
    // 更新角色
    await character.update({
      name: name || character.name,
      type: updatedType,
      description: description || character.description,
      avatar_url: finalAvatarUrl || character.avatar_url,
      personality_template: personality_template || character.personality_template,
      prompt_template: prompt_template || character.prompt_template,
      is_public: is_public !== undefined ? !!is_public : character.is_public
    });
    
    res.json({
      code: 0,
      message: '更新角色成功',
      data: character
    });
  } catch (error) {
    console.error('更新角色失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新角色失败'
    });
  }
};

// 删除角色
export const deleteCharacter = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    // 查找角色
    const character = await Character.findByPk(id);
    
    if (!character) {
      res.status(404).json({
        code: 404,
        message: '角色不存在'
      });
      return;
    }
    
    // 检查删除权限
    if (character.tenant_id !== req.user?.tenant_id && req.user?.role !== 'admin') {
      res.status(403).json({
        code: 403,
        message: '无权删除该角色'
      });
      return;
    }
    
    // 删除角色
    await character.destroy();
    
    res.json({
      code: 0,
      message: '删除角色成功'
    });
  } catch (error) {
    console.error('删除角色失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除角色失败'
    });
  }
};

// 获取角色类型列表
export const getCharacterTypes = async (req: Request, res: Response): Promise<void> => {
  try {
    const types = Object.values(CharacterType).map(type => ({
      value: type,
      label: getCharacterTypeLabel(type)
    }));
    
    res.json({
      code: 0,
      message: '获取角色类型列表成功',
      data: types
    });
  } catch (error) {
    console.error('获取角色类型列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取角色类型列表失败'
    });
  }
};

// 获取角色类型标签
const getCharacterTypeLabel = (type: string): string => {
  const typeMap: Record<string, string> = {
    [CharacterType.PET]: '萌宠',
    [CharacterType.ASSISTANT]: '助手',
    [CharacterType.COMPANION]: '伙伴',
    [CharacterType.MENTOR]: '导师',
    [CharacterType.ENTERTAINMENT]: '娱乐'
  };
  
  return typeMap[type] || type;
}; 
