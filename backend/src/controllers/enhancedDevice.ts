import { Request, Response } from 'express'
import { pool } from '../config/database'
import { externalMqttService } from '../services/externalMqttService'

interface AuthRequest extends Request {
  user?: {
    id: number
    username: string
    tenant_id: number | null
    role: string
  }
  userPermission?: {
    canAccessAllData: boolean
    canAccessTenantData: boolean
    tenantId: number | null
    userId: number
    role: string
  }
}

// 获取设备列表
export const getDevices = async (req: AuthRequest, res: Response) => {
  try {
    const { page = 1, limit = 10, search, status, character_id } = req.query
    const currentUser = req.user!
    const permission = req.userPermission!

    let whereClause = 'WHERE 1=1'
    const params: any[] = []

    // 根据权限过滤数据
    if (!permission.canAccessAllData) {
      if (permission.canAccessTenantData) {
        whereClause += ' AND (d.user_id = ? OR d.tenant_id = ?)'
        params.push(currentUser.id, currentUser.tenant_id)
      } else {
        whereClause += ' AND d.user_id = ?'
        params.push(currentUser.id)
      }
    }

    // 搜索条件
    if (search) {
      whereClause += ' AND (d.name LIKE ? OR d.device_id LIKE ?)'
      const searchTerm = `%${search}%`
      params.push(searchTerm, searchTerm)
    }

    // 状态筛选
    if (status) {
      whereClause += ' AND d.status = ?'
      params.push(status)
    }

    // 角色筛选
    if (character_id) {
      whereClause += ' AND d.character_id = ?'
      params.push(character_id)
    }

    // 计算总数
    const [countResult]: any = await pool.execute(
      `SELECT COUNT(*) as total FROM devices d ${whereClause}`,
      params
    )
    const total = countResult[0].total

    // 分页查询
    const offset = (Number(page) - 1) * Number(limit)
    const [rows]: any = await pool.execute(
      `SELECT d.*, c.name as character_name, u.username as owner_name
       FROM devices d
       LEFT JOIN characters c ON d.character_id = c.id
       LEFT JOIN users u ON d.user_id = u.id
       ${whereClause}
       ORDER BY d.created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, Number(limit), offset]
    )

    res.json({
      code: 0,
      data: {
        items: rows.map((row: any) => ({
          ...row,
          cooldown_policy: typeof row.cooldown_policy === 'string' 
            ? JSON.parse(row.cooldown_policy) 
            : row.cooldown_policy,
          is_owner: row.user_id === currentUser.id
        })),
        total,
        page: Number(page),
        limit: Number(limit)
      }
    })
  } catch (error) {
    console.error('获取设备列表失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 创建设备
export const createDevice = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id, name, character_id, model_key, cooldown_policy } = req.body
    const currentUser = req.user!

    // 验证必填字段
    if (!device_id || !name || !character_id || !model_key) {
      return res.status(400).json({
        code: 400,
        message: '设备ID、名称、角色ID和模型标识不能为空'
      })
    }

    // 检查设备ID是否已存在
    const [existing]: any = await pool.execute(
      'SELECT id FROM devices WHERE device_id = ?',
      [device_id]
    )

    if (existing.length > 0) {
      return res.status(400).json({
        code: 400,
        message: '设备ID已存在'
      })
    }

    // 检查角色是否存在且用户有权使用
    const [characterRows]: any = await pool.execute(
      'SELECT id, user_id, is_public FROM characters WHERE id = ?',
      [character_id]
    )

    if (characterRows.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '角色不存在'
      })
    }

    const character = characterRows[0]
    if (character.user_id !== currentUser.id && !character.is_public) {
      return res.status(403).json({
        code: 403,
        message: '无权使用此角色'
      })
    }

    // 检查模型是否存在
    const [modelRows]: any = await pool.execute(
      'SELECT id FROM models WHERE model_key = ? AND status = 1',
      [model_key]
    )

    if (modelRows.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '模型不存在或已禁用'
      })
    }

    // 默认冷却策略
    const defaultCooldownPolicy = {
      max_requests_per_minute: 10,
      cooldown_seconds: 5
    }

    // 创建设备
    const [result]: any = await pool.execute(
      `INSERT INTO devices (device_id, name, user_id, tenant_id, character_id, model_key, status, cooldown_policy)
       VALUES (?, ?, ?, ?, ?, ?, 'offline', ?)`,
      [
        device_id,
        name,
        currentUser.id,
        currentUser.tenant_id,
        character_id,
        model_key,
        JSON.stringify(cooldown_policy || defaultCooldownPolicy)
      ]
    )

    // 自动订阅新设备的MQTT主题
    try {
      const subscribed = await externalMqttService.addDeviceSubscription(device_id)
      if (subscribed) {
        console.log(`✅ 新设备 ${device_id} 已自动订阅MQTT主题`)
      } else {
        console.warn(`⚠️ 设备 ${device_id} MQTT主题订阅失败，但设备创建成功`)
      }
    } catch (error) {
      console.error(`设备 ${device_id} MQTT订阅失败:`, error)
      // 不影响设备创建，只记录错误
    }

    res.json({
      code: 0,
      message: '设备创建成功',
      data: {
        id: result.insertId,
        device_id,
        name,
        status: 'offline'
      }
    })
  } catch (error) {
    console.error('创建设备失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 获取单个设备详情
export const getDeviceById = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id } = req.params
    const currentUser = req.user!

    const [rows]: any = await pool.execute(
      `SELECT d.*, c.name as character_name, c.type as character_type, 
              c.personality_template, c.prompt_template, u.username as owner_name
       FROM devices d
       LEFT JOIN characters c ON d.character_id = c.id
       LEFT JOIN users u ON d.user_id = u.id
       WHERE d.device_id = ?`,
      [device_id]
    )

    if (rows.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '设备不存在'
      })
    }

    const device = rows[0]
    
    res.json({
      code: 0,
      data: {
        ...device,
        cooldown_policy: typeof device.cooldown_policy === 'string' 
          ? JSON.parse(device.cooldown_policy) 
          : device.cooldown_policy,
        personality_template: typeof device.personality_template === 'string'
          ? JSON.parse(device.personality_template)
          : device.personality_template,
        is_owner: device.user_id === currentUser.id
      }
    })
  } catch (error) {
    console.error('获取设备详情失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 更新设备
export const updateDevice = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id } = req.params
    const { name, character_id, model_key, status, cooldown_policy } = req.body
    const currentUser = req.user!

    // 检查设备是否存在且用户有权限修改
    const [existing]: any = await pool.execute(
      'SELECT * FROM devices WHERE device_id = ?',
      [device_id]
    )

    if (existing.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '设备不存在'
      })
    }

    const device = existing[0]

    // 权限检查
    const canModify = device.user_id === currentUser.id ||
                     (currentUser.role === 'admin' && device.tenant_id === currentUser.tenant_id) ||
                     currentUser.role === 'super_admin'

    if (!canModify) {
      return res.status(403).json({
        code: 403,
        message: '无权修改此设备'
      })
    }

    // 如果修改了角色，检查权限
    if (character_id && character_id !== device.character_id) {
      const [characterRows]: any = await pool.execute(
        'SELECT id, user_id, is_public FROM characters WHERE id = ?',
        [character_id]
      )

      if (characterRows.length === 0) {
        return res.status(400).json({
          code: 400,
          message: '角色不存在'
        })
      }

      const character = characterRows[0]
      if (character.user_id !== currentUser.id && !character.is_public) {
        return res.status(403).json({
          code: 403,
          message: '无权使用此角色'
        })
      }
    }

    // 如果修改了模型，检查是否存在
    if (model_key && model_key !== device.model_key) {
      const [modelRows]: any = await pool.execute(
        'SELECT id FROM models WHERE model_key = ? AND status = 1',
        [model_key]
      )

      if (modelRows.length === 0) {
        return res.status(400).json({
          code: 400,
          message: '模型不存在或已禁用'
        })
      }
    }

    // 更新设备
    await pool.execute(
      `UPDATE devices SET 
       name = ?, character_id = ?, model_key = ?, status = ?, 
       cooldown_policy = ?, updated_at = NOW() 
       WHERE device_id = ?`,
      [
        name || device.name,
        character_id || device.character_id,
        model_key || device.model_key,
        status || device.status,
        cooldown_policy ? JSON.stringify(cooldown_policy) : device.cooldown_policy,
        device_id
      ]
    )

    res.json({
      code: 0,
      message: '设备更新成功'
    })
  } catch (error) {
    console.error('更新设备失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 删除设备
export const deleteDevice = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id } = req.params
    const currentUser = req.user!

    // 检查设备是否存在且用户有权限删除
    const [existing]: any = await pool.execute(
      'SELECT * FROM devices WHERE device_id = ?',
      [device_id]
    )

    if (existing.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '设备不存在'
      })
    }

    const device = existing[0]

    // 权限检查
    const canDelete = device.user_id === currentUser.id ||
                     (currentUser.role === 'admin' && device.tenant_id === currentUser.tenant_id) ||
                     currentUser.role === 'super_admin'

    if (!canDelete) {
      return res.status(403).json({
        code: 403,
        message: '无权删除此设备'
      })
    }

    // 取消设备的MQTT主题订阅
    try {
      const unsubscribed = await externalMqttService.removeDeviceSubscription(device_id)
      if (unsubscribed) {
        console.log(`✅ 设备 ${device_id} 已取消MQTT主题订阅`)
      } else {
        console.warn(`⚠️ 设备 ${device_id} MQTT主题取消订阅失败`)
      }
    } catch (error) {
      console.error(`设备 ${device_id} MQTT取消订阅失败:`, error)
      // 不影响设备删除，只记录错误
    }

    // 删除设备（会级联删除相关的对话历史和记忆）
    await pool.execute('DELETE FROM devices WHERE device_id = ?', [device_id])

    res.json({
      code: 0,
      message: '设备删除成功'
    })
  } catch (error) {
    console.error('删除设备失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}
