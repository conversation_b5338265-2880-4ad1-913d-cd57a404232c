import { Request, Response } from 'express'
import { pool } from '../config/database'

interface AuthRequest extends Request {
  user?: {
    id: number
    username: string
    tenant_id: number | null
    role: string
  }
}

// 获取MQTT连接状态
export const getMqttStatus = async (req: AuthRequest, res: Response) => {
  try {
    // 这里需要从MQTT服务实例获取状态
    // 暂时返回模拟状态
    const status = {
      connected: true,
      server: '175.27.168.104:10079',
      reconnectAttempts: 0,
      lastConnected: new Date().toISOString(),
      subscribedTopics: [
        'xstar/callback',
        'xstar/heartbeat'
      ]
    }

    res.json({
      code: 0,
      data: status
    })
  } catch (error) {
    console.error('获取MQTT状态失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 获取设备MQTT活动日志
export const getDeviceMqttLogs = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id } = req.params
    const { page = 1, limit = 20, type } = req.query
    const currentUser = req.user!

    if (!device_id) {
      return res.status(400).json({
        code: 400,
        message: '设备ID不能为空'
      })
    }

    // 验证设备访问权限
    const [deviceRows]: any = await pool.execute(
      'SELECT user_id, tenant_id FROM devices WHERE device_id = ?',
      [device_id]
    )

    if (deviceRows.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '设备不存在'
      })
    }

    const device = deviceRows[0]
    const canAccess = device.user_id === currentUser.id ||
                     (currentUser.role === 'admin' && device.tenant_id === currentUser.tenant_id) ||
                     currentUser.role === 'super_admin'

    if (!canAccess) {
      return res.status(403).json({
        code: 403,
        message: '无权访问此设备'
      })
    }

    // 构建查询条件
    let whereClause = 'WHERE device_id = ?'
    const params: any[] = [device_id]

    // 按消息类型过滤
    if (type && ['received', 'sent'].includes(type as string)) {
      whereClause += ' AND direction = ?'
      params.push(type)
    }

    // 计算总数
    const [countResult]: any = await pool.execute(
      `SELECT COUNT(*) as total FROM mqtt_logs ${whereClause}`,
      params
    )
    const total = countResult[0].total

    // 分页查询日志
    const offset = (Number(page) - 1) * Number(limit)
    const [logs]: any = await pool.execute(
      `SELECT id, direction, topic, message, message_size, device_id, xstar_id,
              success, error_message, timestamp
       FROM mqtt_logs ${whereClause}
       ORDER BY timestamp DESC
       LIMIT ? OFFSET ?`,
      [...params, Number(limit), offset]
    )

    // 格式化日志数据
    const items = logs.map((log: any) => {
      let messagePreview = log.message
      let messageType = 'unknown'

      try {
        const parsed = JSON.parse(log.message)
        if (parsed.user_input) {
          messagePreview = `用户输入: ${parsed.user_input}`
          messageType = 'callback'
        } else if (parsed.response_text) {
          messagePreview = `AI回复: ${parsed.response_text}`
          messageType = 'response'
        } else if (parsed.battery_level !== undefined) {
          messagePreview = `心跳: 电量${parsed.battery_level}%, WiFi${(parsed.wifi_strength * 100).toFixed(1)}%`
          messageType = 'heartbeat'
        } else if (parsed.error) {
          messagePreview = `错误: ${parsed.message || parsed.error}`
          messageType = 'error'
        } else if (parsed.status === 'ok') {
          messagePreview = `心跳确认: ${parsed.server_time}`
          messageType = 'heartbeat_ack'
        }
      } catch (e) {
        // 保持原始消息
        if (log.message.length > 100) {
          messagePreview = log.message.substring(0, 100) + '...'
        }
      }

      return {
        id: log.id,
        device_id: log.device_id,
        xstar_id: log.xstar_id,
        direction: log.direction,
        topic: log.topic,
        message: log.message,
        message_preview: messagePreview,
        message_type: messageType,
        message_size: log.message_size,
        success: log.success,
        error_message: log.error_message,
        timestamp: log.timestamp
      }
    })

    res.json({
      code: 0,
      data: {
        items,
        total,
        page: Number(page),
        limit: Number(limit)
      }
    })
  } catch (error) {
    console.error('获取设备MQTT日志失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 模拟发送MQTT消息（用于测试）
export const sendTestMessage = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id, message_type, content } = req.body
    const currentUser = req.user!

    if (!device_id || !message_type) {
      return res.status(400).json({
        code: 400,
        message: '设备ID和消息类型不能为空'
      })
    }

    // 验证设备访问权限
    const [deviceRows]: any = await pool.execute(
      'SELECT user_id, tenant_id FROM devices WHERE device_id = ?',
      [device_id]
    )

    if (deviceRows.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '设备不存在'
      })
    }

    const device = deviceRows[0]
    const canAccess = device.user_id === currentUser.id ||
                     (currentUser.role === 'admin' && device.tenant_id === currentUser.tenant_id) ||
                     currentUser.role === 'super_admin'

    if (!canAccess) {
      return res.status(403).json({
        code: 403,
        message: '无权访问此设备'
      })
    }

    // 根据消息类型构建测试消息
    let testMessage: any
    let topic: string

    switch (message_type) {
      case 'response':
        topic = `vendor/${device_id}/response`
        testMessage = {
          device_id,
          response_text: content || '这是一条测试回复',
          screen: {
            image_url: 'https://assets.ai-doll.cn/emotions/happy.gif',
            image_timer: 100,
            image_counter: 30,
            image_mode: 'once'
          },
          servos: {
            left_ear: [1, 0, 1, 0],
            right_ear: [1, 0, 1, 0],
            tail: [1, 1, 1, 1]
          },
          timestamp: Date.now()
        }
        break

      case 'heartbeat_ack':
        topic = `vendor/${device_id}/heartbeat_ack`
        testMessage = {
          status: 'ok',
          server_time: new Date().toISOString(),
          device_id
        }
        break

      case 'error':
        topic = `vendor/${device_id}/error`
        testMessage = {
          error: '测试错误',
          message: content || '这是一条测试错误消息',
          code: 400,
          timestamp: new Date().toISOString()
        }
        break

      default:
        return res.status(400).json({
          code: 400,
          message: '不支持的消息类型'
        })
    }

    // 这里应该通过MQTT客户端发送消息
    // 暂时返回成功响应
    console.log(`模拟发送MQTT消息到 ${topic}:`, testMessage)

    res.json({
      code: 0,
      message: '测试消息发送成功',
      data: {
        topic,
        message: testMessage,
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('发送测试消息失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 获取设备硬件状态
export const getDeviceHardwareStatus = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id } = req.params
    const currentUser = req.user!

    if (!device_id) {
      return res.status(400).json({
        code: 400,
        message: '设备ID不能为空'
      })
    }

    // 验证设备访问权限
    const [deviceRows]: any = await pool.execute(
      'SELECT user_id, tenant_id, cooldown_policy, last_heartbeat, status FROM devices WHERE device_id = ?',
      [device_id]
    )

    if (deviceRows.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '设备不存在'
      })
    }

    const device = deviceRows[0]
    const canAccess = device.user_id === currentUser.id ||
                     (currentUser.role === 'admin' && device.tenant_id === currentUser.tenant_id) ||
                     currentUser.role === 'super_admin'

    if (!canAccess) {
      return res.status(403).json({
        code: 403,
        message: '无权访问此设备'
      })
    }

    // 解析设备状态信息
    const cooldownPolicy = typeof device.cooldown_policy === 'string' 
      ? JSON.parse(device.cooldown_policy) 
      : device.cooldown_policy

    const hardwareStatus = {
      device_id,
      status: device.status,
      last_heartbeat: device.last_heartbeat,
      battery_level: cooldownPolicy?.battery_level || 0,
      wifi_strength: cooldownPolicy?.wifi_strength || 0,
      online: device.status === 'online' && 
              device.last_heartbeat && 
              (Date.now() - new Date(device.last_heartbeat).getTime()) < 300000, // 5分钟内有心跳
      hardware_info: {
        screen: {
          status: 'normal',
          current_image: 'https://assets.ai-doll.cn/emotions/neutral.gif'
        },
        servos: {
          left_ear: { position: 0, status: 'normal' },
          right_ear: { position: 0, status: 'normal' },
          tail: { position: 0, status: 'normal' }
        }
      }
    }

    res.json({
      code: 0,
      data: hardwareStatus
    })
  } catch (error) {
    console.error('获取设备硬件状态失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 获取MQTT统计信息
export const getMqttStats = async (req: AuthRequest, res: Response) => {
  try {
    const currentUser = req.user!

    // 根据用户权限获取统计数据
    let whereClause = ''
    const params: any[] = []

    if (currentUser.role === 'super_admin') {
      // 超级管理员可以看到所有统计
      whereClause = ''
    } else if (currentUser.role === 'admin' && currentUser.tenant_id) {
      // 租户管理员只能看到本租户的统计
      whereClause = 'WHERE tenant_id = ?'
      params.push(currentUser.tenant_id)
    } else {
      // 普通用户只能看到自己的统计
      whereClause = 'WHERE user_id = ?'
      params.push(currentUser.id)
    }

    // 获取设备统计
    const [deviceStats]: any = await pool.execute(
      `SELECT 
         COUNT(*) as total_devices,
         SUM(CASE WHEN status = 'online' THEN 1 ELSE 0 END) as online_devices,
         SUM(CASE WHEN last_heartbeat > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 1 ELSE 0 END) as active_devices
       FROM devices ${whereClause}`,
      params
    )

    // 模拟MQTT消息统计
    const mqttStats = {
      devices: deviceStats[0],
      messages: {
        total_today: 1250,
        callbacks_today: 800,
        heartbeats_today: 400,
        errors_today: 50
      },
      performance: {
        avg_response_time: 150, // 毫秒
        success_rate: 0.96,
        last_24h_uptime: 0.998
      }
    }

    res.json({
      code: 0,
      data: mqttStats
    })
  } catch (error) {
    console.error('获取MQTT统计失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 获取MQTT消息统计
export const getMqttMessageStats = async (req: AuthRequest, res: Response) => {
  try {
    const { device_id, hours = 24 } = req.query
    const currentUser = req.user!

    // 构建查询条件
    let whereClause = 'WHERE timestamp >= DATE_SUB(NOW(), INTERVAL ? HOUR)'
    const params: any[] = [Number(hours)]

    // 如果指定了设备ID，验证权限并添加过滤
    if (device_id) {
      const [deviceRows]: any = await pool.execute(
        'SELECT user_id, tenant_id FROM devices WHERE device_id = ?',
        [device_id]
      )

      if (deviceRows.length === 0) {
        return res.status(404).json({
          code: 404,
          message: '设备不存在'
        })
      }

      const device = deviceRows[0]
      const canAccess = device.user_id === currentUser.id ||
                       (currentUser.role === 'admin' && device.tenant_id === currentUser.tenant_id) ||
                       currentUser.role === 'super_admin'

      if (!canAccess) {
        return res.status(403).json({
          code: 403,
          message: '无权访问此设备'
        })
      }

      whereClause += ' AND device_id = ?'
      params.push(device_id)
    } else {
      // 根据用户权限过滤数据
      if (currentUser.role === 'user') {
        // 普通用户只能看自己设备的数据
        const [userDevices]: any = await pool.execute(
          'SELECT device_id FROM devices WHERE user_id = ?',
          [currentUser.id]
        )

        if (userDevices.length > 0) {
          const deviceIds = userDevices.map((d: any) => d.device_id)
          whereClause += ` AND device_id IN (${deviceIds.map(() => '?').join(',')})`
          params.push(...deviceIds)
        } else {
          whereClause += ' AND 1=0' // 没有设备时返回空结果
        }
      } else if (currentUser.role === 'admin' && currentUser.tenant_id) {
        // 租户管理员只能看本租户的数据
        const [tenantDevices]: any = await pool.execute(
          'SELECT device_id FROM devices WHERE tenant_id = ?',
          [currentUser.tenant_id]
        )

        if (tenantDevices.length > 0) {
          const deviceIds = tenantDevices.map((d: any) => d.device_id)
          whereClause += ` AND device_id IN (${deviceIds.map(() => '?').join(',')})`
          params.push(...deviceIds)
        } else {
          whereClause += ' AND 1=0'
        }
      }
      // super_admin 可以看所有数据，不需要额外过滤
    }

    // 获取消息统计
    const [stats]: any = await pool.execute(`
      SELECT
        direction,
        success,
        COUNT(*) as count,
        SUM(message_size) as total_size,
        AVG(message_size) as avg_size
      FROM mqtt_logs
      ${whereClause}
      GROUP BY direction, success
    `, params)

    // 获取主题统计
    const [topicStats]: any = await pool.execute(`
      SELECT
        topic,
        direction,
        COUNT(*) as count,
        SUM(message_size) as total_size
      FROM mqtt_logs
      ${whereClause}
      GROUP BY topic, direction
      ORDER BY count DESC
      LIMIT 20
    `, params)

    // 获取时间分布统计
    const [timeStats]: any = await pool.execute(`
      SELECT
        DATE_FORMAT(timestamp, '%Y-%m-%d %H:00:00') as hour,
        direction,
        COUNT(*) as count
      FROM mqtt_logs
      ${whereClause}
      GROUP BY hour, direction
      ORDER BY hour DESC
    `, params)

    // 格式化统计数据
    const summary = {
      received: { success: 0, failed: 0, total_size: 0 },
      sent: { success: 0, failed: 0, total_size: 0 }
    }

    stats.forEach((stat: any) => {
      const direction = stat.direction as 'received' | 'sent'
      if (stat.success) {
        summary[direction].success = stat.count
        summary[direction].total_size += stat.total_size || 0
      } else {
        summary[direction].failed = stat.count
      }
    })

    res.json({
      code: 0,
      data: {
        summary,
        topic_stats: topicStats,
        time_distribution: timeStats,
        query_params: {
          device_id: device_id || 'all',
          hours: Number(hours)
        }
      }
    })
  } catch (error) {
    console.error('获取MQTT消息统计失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}
