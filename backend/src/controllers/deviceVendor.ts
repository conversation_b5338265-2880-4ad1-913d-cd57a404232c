import { Request, Response } from 'express'
import { pool } from '../config/database'
import crypto from 'crypto'

interface AuthRequest extends Request {
  user?: {
    id: number
    username: string
    tenant_id: number | null
    role: string
  }
}

// 获取厂商列表
export const getVendors = async (req: AuthRequest, res: Response) => {
  try {
    const { page = 1, limit = 10, search, status } = req.query
    const currentUser = req.user!

    let whereClause = 'WHERE 1=1'
    const params: any[] = []

    // 权限控制：普通用户只能看到自己的厂商
    if (currentUser.role === 'user') {
      whereClause += ' AND user_id = ?'
      params.push(currentUser.id)
    } else if (currentUser.role === 'admin' && currentUser.tenant_id) {
      whereClause += ' AND tenant_id = ?'
      params.push(currentUser.tenant_id)
    }

    // 搜索条件
    if (search) {
      whereClause += ' AND (vendor_id LIKE ? OR name LIKE ?)'
      const searchTerm = `%${search}%`
      params.push(searchTerm, searchTerm)
    }

    // 状态筛选
    if (status) {
      whereClause += ' AND status = ?'
      params.push(status)
    }

    // 计算总数
    const [countResult]: any = await pool.execute(
      `SELECT COUNT(*) as total FROM device_vendors ${whereClause}`,
      params
    )
    const total = countResult[0].total

    // 分页查询
    const offset = (Number(page) - 1) * Number(limit)
    const [rows]: any = await pool.execute(
      `SELECT dv.*, u.username as creator_name 
       FROM device_vendors dv 
       LEFT JOIN users u ON dv.user_id = u.id 
       ${whereClause} 
       ORDER BY dv.created_at DESC 
       LIMIT ? OFFSET ?`,
      [...params, Number(limit), offset]
    )

    res.json({
      code: 0,
      data: {
        items: rows.map((row: any) => ({
          ...row,
          // 隐藏API密钥，只显示前几位
          api_key: row.api_key ? `${row.api_key.substring(0, 8)}****` : null
        })),
        total,
        page: Number(page),
        limit: Number(limit)
      }
    })
  } catch (error) {
    console.error('获取厂商列表失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 创建厂商
export const createVendor = async (req: AuthRequest, res: Response) => {
  try {
    const { xstar_id, name, description, contact_info } = req.body
    const currentUser = req.user!

    // 验证必填字段
    if (!xstar_id || !name) {
      return res.status(400).json({
        code: 400,
        message: '厂商ID和名称不能为空'
      })
    }

    // 检查厂商ID是否已存在
    const [existing]: any = await pool.execute(
      'SELECT id FROM device_vendors WHERE xstar_id = ?',
      [xstar_id]
    )

    if (existing.length > 0) {
      return res.status(400).json({
        code: 400,
        message: '厂商ID已存在'
      })
    }

    // 生成API密钥
    const apiKey = crypto.randomBytes(32).toString('hex')

    // 创建厂商
    const [result]: any = await pool.execute(
      `INSERT INTO device_vendors (xstar_id, name, description, contact_info, api_key, user_id, tenant_id, status)
       VALUES (?, ?, ?, ?, ?, ?, ?, 'active')`,
      [xstar_id, name, description || '', contact_info || '', apiKey, currentUser.id, currentUser.tenant_id]
    )

    res.json({
      code: 0,
      message: '厂商创建成功',
      data: {
        id: result.insertId,
        xstar_id,
        name,
        api_key: apiKey // 创建时返回完整API密钥
      }
    })
  } catch (error) {
    console.error('创建厂商失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 更新厂商
export const updateVendor = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params
    const { name, description, contact_info, status } = req.body
    const currentUser = req.user!

    // 检查厂商是否存在且用户有权限修改
    const [existing]: any = await pool.execute(
      'SELECT * FROM device_vendors WHERE id = ?',
      [id]
    )

    if (existing.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '厂商不存在'
      })
    }

    const vendor = existing[0]

    // 权限检查
    const canModify = vendor.user_id === currentUser.id ||
                     (currentUser.role === 'admin' && vendor.tenant_id === currentUser.tenant_id) ||
                     currentUser.role === 'super_admin'

    if (!canModify) {
      return res.status(403).json({
        code: 403,
        message: '无权修改此厂商'
      })
    }

    // 更新厂商信息
    await pool.execute(
      `UPDATE device_vendors SET 
       name = ?, description = ?, contact_info = ?, status = ?, updated_at = NOW() 
       WHERE id = ?`,
      [
        name || vendor.name,
        description !== undefined ? description : vendor.description,
        contact_info !== undefined ? contact_info : vendor.contact_info,
        status || vendor.status,
        id
      ]
    )

    res.json({
      code: 0,
      message: '厂商更新成功'
    })
  } catch (error) {
    console.error('更新厂商失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 重新生成API密钥
export const regenerateApiKey = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params
    const currentUser = req.user!

    // 检查厂商是否存在且用户有权限修改
    const [existing]: any = await pool.execute(
      'SELECT * FROM device_vendors WHERE id = ?',
      [id]
    )

    if (existing.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '厂商不存在'
      })
    }

    const vendor = existing[0]

    // 权限检查
    const canModify = vendor.user_id === currentUser.id ||
                     (currentUser.role === 'admin' && vendor.tenant_id === currentUser.tenant_id) ||
                     currentUser.role === 'super_admin'

    if (!canModify) {
      return res.status(403).json({
        code: 403,
        message: '无权修改此厂商'
      })
    }

    // 生成新的API密钥
    const newApiKey = crypto.randomBytes(32).toString('hex')

    // 更新API密钥
    await pool.execute(
      'UPDATE device_vendors SET api_key = ?, updated_at = NOW() WHERE id = ?',
      [newApiKey, id]
    )

    res.json({
      code: 0,
      message: 'API密钥重新生成成功',
      data: {
        api_key: newApiKey
      }
    })
  } catch (error) {
    console.error('重新生成API密钥失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 删除厂商
export const deleteVendor = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params
    const currentUser = req.user!

    // 检查厂商是否存在且用户有权限删除
    const [existing]: any = await pool.execute(
      'SELECT * FROM device_vendors WHERE id = ?',
      [id]
    )

    if (existing.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '厂商不存在'
      })
    }

    const vendor = existing[0]

    // 权限检查
    const canDelete = vendor.user_id === currentUser.id ||
                     (currentUser.role === 'admin' && vendor.tenant_id === currentUser.tenant_id) ||
                     currentUser.role === 'super_admin'

    if (!canDelete) {
      return res.status(403).json({
        code: 403,
        message: '无权删除此厂商'
      })
    }

    // 检查是否有关联的设备
    const [devices]: any = await pool.execute(
      'SELECT COUNT(*) as count FROM devices WHERE JSON_EXTRACT(cooldown_policy, "$.xstar_id") = ?',
      [vendor.xstar_id]
    )

    if (devices[0].count > 0) {
      return res.status(400).json({
        code: 400,
        message: '该厂商下还有关联设备，无法删除'
      })
    }

    // 删除厂商
    await pool.execute('DELETE FROM device_vendors WHERE id = ?', [id])

    res.json({
      code: 0,
      message: '厂商删除成功'
    })
  } catch (error) {
    console.error('删除厂商失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 获取厂商统计信息
export const getVendorStats = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params
    const currentUser = req.user!

    // 检查厂商是否存在且用户有权限访问
    const [existing]: any = await pool.execute(
      'SELECT * FROM device_vendors WHERE id = ?',
      [id]
    )

    if (existing.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '厂商不存在'
      })
    }

    const vendor = existing[0]

    // 权限检查
    const canAccess = vendor.user_id === currentUser.id ||
                     (currentUser.role === 'admin' && vendor.tenant_id === currentUser.tenant_id) ||
                     currentUser.role === 'super_admin'

    if (!canAccess) {
      return res.status(403).json({
        code: 403,
        message: '无权访问此厂商'
      })
    }

    // 获取统计信息（这里是模拟数据，实际应该从日志表获取）
    const stats = {
      xstar_info: {
        xstar_id: vendor.xstar_id,
        name: vendor.name,
        status: vendor.status,
        created_at: vendor.created_at
      },
      devices: {
        total: 0,
        online: 0,
        offline: 0
      },
      messages: {
        total_today: 0,
        callbacks_today: 0,
        heartbeats_today: 0,
        errors_today: 0
      },
      performance: {
        avg_response_time: 0,
        success_rate: 0,
        last_active: null
      }
    }

    res.json({
      code: 0,
      data: stats
    })
  } catch (error) {
    console.error('获取厂商统计失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}
