import { Request, Response } from 'express'
import { AliyunModelService } from '../services/aliyunModelService'
import { pool } from '../config/database'

interface AuthRequest extends Request {
  user?: {
    id: number
    username: string
    tenant_id: number | null
    role: string
  }
}

// 测试阿里云模型连接
export const testAliyunModel = async (req: AuthRequest, res: Response) => {
  try {
    const { api_key, model = 'qwen-turbo', test_message = '你好' } = req.body

    if (!api_key) {
      return res.status(400).json({
        code: 400,
        message: 'API Key不能为空'
      })
    }

    // 创建阿里云模型服务实例
    const aliyunService = new AliyunModelService({
      api_key,
      model
    })

    // 测试调用
    const startTime = Date.now()
    const response = await aliyunService.chat({
      messages: [{ role: 'user', content: test_message }],
      max_tokens: 100
    })
    const responseTime = Date.now() - startTime

    res.json({
      code: 0,
      message: '测试成功',
      data: {
        model: response.model,
        response: response.content,
        usage: response.usage,
        response_time: responseTime,
        finish_reason: response.finish_reason
      }
    })
  } catch (error: any) {
    console.error('阿里云模型测试失败:', error)
    res.status(500).json({
      code: 500,
      message: `测试失败: ${error.message}`
    })
  }
}

// 获取支持的阿里云模型列表
export const getSupportedModels = async (req: Request, res: Response) => {
  try {
    const models = AliyunModelService.getSupportedModels()
    const modelDetails = models.map(model => ({
      model,
      ...AliyunModelService.getModelInfo(model)
    }))

    res.json({
      code: 0,
      data: modelDetails
    })
  } catch (error) {
    console.error('获取模型列表失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 配置阿里云模型
export const configureAliyunModel = async (req: AuthRequest, res: Response) => {
  try {
    const { model_key, name, model, api_key, api_url, temperature, max_tokens } = req.body

    if (!model_key || !name || !model || !api_key) {
      return res.status(400).json({
        code: 400,
        message: '模型标识、名称、模型名和API Key不能为空'
      })
    }

    // 检查模型是否已存在
    const [existing]: any = await pool.execute(
      'SELECT id FROM models WHERE model_key = ?',
      [model_key]
    )

    const config = {
      provider: 'aliyun',
      model,
      api_key,
      api_url: api_url || 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
      temperature: temperature || 0.7,
      max_tokens: max_tokens || 2048
    }

    if (existing.length > 0) {
      // 更新现有模型
      await pool.execute(
        'UPDATE models SET name = ?, config = ?, updated_at = NOW() WHERE model_key = ?',
        [name, JSON.stringify(config), model_key]
      )
    } else {
      // 创建新模型
      await pool.execute(
        'INSERT INTO models (model_key, name, type, config, status) VALUES (?, ?, ?, ?, 1)',
        [model_key, name, 'cloud', JSON.stringify(config)]
      )
    }

    res.json({
      code: 0,
      message: existing.length > 0 ? '模型配置更新成功' : '模型配置创建成功'
    })
  } catch (error) {
    console.error('配置阿里云模型失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 测试角色对话
export const testCharacterChat = async (req: AuthRequest, res: Response) => {
  try {
    const { 
      api_key, 
      model = 'qwen-turbo', 
      character_name, 
      character_type, 
      personality, 
      prompt_template, 
      user_input 
    } = req.body

    if (!api_key || !character_name || !user_input) {
      return res.status(400).json({
        code: 400,
        message: 'API Key、角色名称和用户输入不能为空'
      })
    }

    // 创建阿里云模型服务实例
    const aliyunService = new AliyunModelService({
      api_key,
      model
    })

    // 测试角色对话
    const startTime = Date.now()
    const response = await aliyunService.generateCharacterResponse(
      user_input,
      character_name,
      character_type || 'assistant',
      personality || { friendliness: 80, intelligence: 70 },
      prompt_template || '你是一个友善的AI助手，请用温和的语气回复用户。'
    )
    const responseTime = Date.now() - startTime

    res.json({
      code: 0,
      message: '角色对话测试成功',
      data: {
        character_name,
        user_input,
        ai_response: response.content,
        model: response.model,
        usage: response.usage,
        response_time: responseTime
      }
    })
  } catch (error: any) {
    console.error('角色对话测试失败:', error)
    res.status(500).json({
      code: 500,
      message: `测试失败: ${error.message}`
    })
  }
}

// 估算token使用量
export const estimateTokens = async (req: Request, res: Response) => {
  try {
    const { text } = req.body

    if (!text) {
      return res.status(400).json({
        code: 400,
        message: '文本内容不能为空'
      })
    }

    const estimatedTokens = AliyunModelService.estimateTokens(text)

    res.json({
      code: 0,
      data: {
        text_length: text.length,
        estimated_tokens: estimatedTokens,
        text_preview: text.substring(0, 100) + (text.length > 100 ? '...' : '')
      }
    })
  } catch (error) {
    console.error('Token估算失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 批量健康检查
export const batchHealthCheck = async (req: AuthRequest, res: Response) => {
  try {
    // 查询所有阿里云模型配置
    const [models]: any = await pool.execute(
      'SELECT model_key, name, config FROM models WHERE JSON_EXTRACT(config, "$.provider") = "aliyun" AND status = 1'
    )

    const results = []

    for (const model of models) {
      try {
        const config = JSON.parse(model.config)
        const aliyunService = new AliyunModelService({
          api_key: config.api_key,
          model: config.model
        })

        const isHealthy = await aliyunService.healthCheck()
        results.push({
          model_key: model.model_key,
          name: model.name,
          model: config.model,
          healthy: isHealthy,
          status: isHealthy ? 'online' : 'offline'
        })
      } catch (error: any) {
        results.push({
          model_key: model.model_key,
          name: model.name,
          healthy: false,
          status: 'error',
          error: error.message
        })
      }
    }

    res.json({
      code: 0,
      data: {
        total: results.length,
        healthy: results.filter(r => r.healthy).length,
        results
      }
    })
  } catch (error) {
    console.error('批量健康检查失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}
