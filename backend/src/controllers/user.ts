import { Request, Response } from 'express'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { pool } from '../config/database'

interface AuthRequest extends Request {
  user?: {
    id: number
    username: string
    tenant_id: number | null
    role: string
  }
}

// 用户注册
export const register = async (req: Request, res: Response) => {
  try {
    const { username, password, email, nickname, tenant_id } = req.body

    // 验证参数
    if (!username || !password) {
      return res.status(400).json({
        code: 400,
        message: '用户名和密码不能为空'
      })
    }

    // 检查用户名是否已存在
    const [existing]: any = await pool.execute(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    )

    if (existing.length > 0) {
      return res.status(400).json({
        code: 400,
        message: '用户名或邮箱已存在'
      })
    }

    // 密码加密
    const salt = await bcrypt.genSalt(10)
    const hashedPassword = await bcrypt.hash(password, salt)

    // 创建用户
    const [result]: any = await pool.execute(
      'INSERT INTO users (username, password, email, nickname, tenant_id, role, status) VALUES (?, ?, ?, ?, ?, ?, 1)',
      [username, hashedPassword, email, nickname, tenant_id, 'user']
    )

    res.json({
      code: 0,
      message: '注册成功',
      data: {
        id: result.insertId,
        username,
        email,
        nickname
      }
    })
  } catch (error) {
    console.error('Register error:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 用户登录
export const login = async (req: Request, res: Response) => {
  try {
    const { username, password } = req.body

    // 验证参数
    if (!username || !password) {
      return res.status(400).json({
        code: 400,
        message: '用户名和密码不能为空'
      })
    }

    // 查询用户
    const [rows]: any = await pool.execute(
      'SELECT * FROM users WHERE username = ? OR email = ?',
      [username, username]
    )

    if (rows.length === 0) {
      return res.status(401).json({
        code: 401,
        message: '用户名或密码错误'
      })
    }

    const user = rows[0]

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return res.status(401).json({
        code: 401,
        message: '用户名或密码错误'
      })
    }

    // 检查用户状态
    if (user.status !== 1) {
      return res.status(403).json({
        code: 403,
        message: '账号已被禁用'
      })
    }

    // 更新最后登录时间
    await pool.execute(
      'UPDATE users SET last_login_at = NOW() WHERE id = ?',
      [user.id]
    )

    // 生成 token
    const token = jwt.sign(
      { 
        id: user.id, 
        username: user.username, 
        tenant_id: user.tenant_id,
        role: user.role
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    )

    // 返回用户信息
    res.json({
      code: 0,
      data: {
        token,
        userInfo: {
          id: user.id,
          username: user.username,
          email: user.email,
          nickname: user.nickname,
          tenantId: user.tenant_id,
          role: user.role,
          avatar_url: user.avatar_url
        }
      }
    })
  } catch (error) {
    console.error('Login error:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 获取用户信息
export const getUserInfo = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user!.id

    // 查询用户信息
    const [rows]: any = await pool.execute(
      'SELECT id, username, email, nickname, tenant_id, role, status, avatar_url, last_login_at FROM users WHERE id = ?',
      [userId]
    )

    if (rows.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      })
    }

    const user = rows[0]

    // 检查用户状态
    if (user.status !== 1) {
      return res.status(403).json({
        code: 403,
        message: '账号已被禁用'
      })
    }

    res.json({
      code: 0,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        nickname: user.nickname,
        tenant_id: user.tenant_id,
        role: user.role,
        avatar_url: user.avatar_url,
        last_login_at: user.last_login_at
      }
    })
  } catch (error) {
    console.error('Get user info error:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 更新用户信息
export const updateUserInfo = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user!.id
    const { nickname, email, avatar_url } = req.body

    // 检查邮箱是否已被其他用户使用
    if (email) {
      const [existing]: any = await pool.execute(
        'SELECT id FROM users WHERE email = ? AND id != ?',
        [email, userId]
      )

      if (existing.length > 0) {
        return res.status(400).json({
          code: 400,
          message: '邮箱已被其他用户使用'
        })
      }
    }

    // 更新用户信息
    await pool.execute(
      'UPDATE users SET nickname = ?, email = ?, avatar_url = ?, updated_at = NOW() WHERE id = ?',
      [nickname, email, avatar_url, userId]
    )

    res.json({
      code: 0,
      message: '更新成功'
    })
  } catch (error) {
    console.error('Update user info error:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 修改密码
export const changePassword = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user!.id
    const { oldPassword, newPassword } = req.body

    if (!oldPassword || !newPassword) {
      return res.status(400).json({
        code: 400,
        message: '旧密码和新密码不能为空'
      })
    }

    // 查询用户当前密码
    const [rows]: any = await pool.execute(
      'SELECT password FROM users WHERE id = ?',
      [userId]
    )

    if (rows.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      })
    }

    const user = rows[0]

    // 验证旧密码
    const isValidPassword = await bcrypt.compare(oldPassword, user.password)
    if (!isValidPassword) {
      return res.status(400).json({
        code: 400,
        message: '旧密码错误'
      })
    }

    // 加密新密码
    const salt = await bcrypt.genSalt(10)
    const hashedNewPassword = await bcrypt.hash(newPassword, salt)

    // 更新密码
    await pool.execute(
      'UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?',
      [hashedNewPassword, userId]
    )

    res.json({
      code: 0,
      message: '密码修改成功'
    })
  } catch (error) {
    console.error('Change password error:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}
