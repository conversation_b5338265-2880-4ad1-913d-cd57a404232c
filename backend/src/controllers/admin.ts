import { Request, Response } from 'express'
import bcrypt from 'bcryptjs'
import { pool } from '../config/database'

interface AuthRequest extends Request {
  user?: {
    id: number
    username: string
    tenant_id: number | null
    role: string
  }
}

// 权限检查中间件
export const checkAdminPermission = (req: AuthRequest, res: Response, next: any) => {
  const userRole = req.user?.role
  
  if (userRole !== 'super_admin' && userRole !== 'admin') {
    return res.status(403).json({
      code: 403,
      message: '权限不足'
    })
  }
  
  next()
}

// 获取用户列表
export const getUserList = async (req: AuthRequest, res: Response) => {
  try {
    const { page = 1, limit = 10, keyword, role, status, tenant_id } = req.query
    const currentUser = req.user!
    
    let whereClause = 'WHERE 1=1'
    const params: any[] = []
    
    // 根据当前用户角色限制查询范围
    if (currentUser.role === 'admin' && currentUser.tenant_id) {
      whereClause += ' AND tenant_id = ?'
      params.push(currentUser.tenant_id)
    }
    
    // 关键词搜索
    if (keyword) {
      whereClause += ' AND (username LIKE ? OR email LIKE ? OR nickname LIKE ?)'
      const searchTerm = `%${keyword}%`
      params.push(searchTerm, searchTerm, searchTerm)
    }
    
    // 角色筛选
    if (role) {
      whereClause += ' AND role = ?'
      params.push(role)
    }
    
    // 状态筛选
    if (status !== undefined) {
      whereClause += ' AND status = ?'
      params.push(status)
    }
    
    // 租户筛选
    if (tenant_id) {
      whereClause += ' AND tenant_id = ?'
      params.push(tenant_id)
    }
    
    // 计算总数
    const [countResult]: any = await pool.execute(
      `SELECT COUNT(*) as total FROM users ${whereClause}`,
      params
    )
    const total = countResult[0].total
    
    // 分页查询
    const offset = (Number(page) - 1) * Number(limit)
    const [rows]: any = await pool.execute(
      `SELECT id, username, email, nickname, tenant_id, role, status, last_login_at, created_at, updated_at 
       FROM users ${whereClause} 
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`,
      [...params, Number(limit), offset]
    )
    
    res.json({
      code: 0,
      data: {
        items: rows,
        total,
        page: Number(page),
        limit: Number(limit)
      }
    })
  } catch (error) {
    console.error('Get user list error:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 创建用户
export const createUser = async (req: AuthRequest, res: Response) => {
  try {
    const { username, password, email, nickname, tenant_id, role = 'user' } = req.body
    const currentUser = req.user!
    
    // 验证参数
    if (!username || !password) {
      return res.status(400).json({
        code: 400,
        message: '用户名和密码不能为空'
      })
    }
    
    // 权限检查：普通管理员只能创建自己租户下的用户
    if (currentUser.role === 'admin' && currentUser.tenant_id && tenant_id !== currentUser.tenant_id) {
      return res.status(403).json({
        code: 403,
        message: '只能创建本租户下的用户'
      })
    }
    
    // 权限检查：普通管理员不能创建管理员用户
    if (currentUser.role === 'admin' && (role === 'admin' || role === 'super_admin')) {
      return res.status(403).json({
        code: 403,
        message: '权限不足，无法创建管理员用户'
      })
    }
    
    // 检查用户名是否已存在
    const [existing]: any = await pool.execute(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    )
    
    if (existing.length > 0) {
      return res.status(400).json({
        code: 400,
        message: '用户名或邮箱已存在'
      })
    }
    
    // 密码加密
    const salt = await bcrypt.genSalt(10)
    const hashedPassword = await bcrypt.hash(password, salt)
    
    // 创建用户
    const [result]: any = await pool.execute(
      'INSERT INTO users (username, password, email, nickname, tenant_id, role, status) VALUES (?, ?, ?, ?, ?, ?, 1)',
      [username, hashedPassword, email, nickname, tenant_id, role]
    )
    
    res.json({
      code: 0,
      message: '用户创建成功',
      data: {
        id: result.insertId,
        username,
        email,
        nickname,
        tenant_id,
        role
      }
    })
  } catch (error) {
    console.error('Create user error:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 更新用户
export const updateUser = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params
    const { username, email, nickname, tenant_id, role, status } = req.body
    const currentUser = req.user!
    
    // 查询目标用户
    const [targetUser]: any = await pool.execute(
      'SELECT * FROM users WHERE id = ?',
      [id]
    )
    
    if (targetUser.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      })
    }
    
    const target = targetUser[0]
    
    // 权限检查：普通管理员只能管理自己租户下的用户
    if (currentUser.role === 'admin' && currentUser.tenant_id && target.tenant_id !== currentUser.tenant_id) {
      return res.status(403).json({
        code: 403,
        message: '只能管理本租户下的用户'
      })
    }
    
    // 权限检查：不能修改超级管理员
    if (target.role === 'super_admin' && currentUser.role !== 'super_admin') {
      return res.status(403).json({
        code: 403,
        message: '权限不足'
      })
    }
    
    // 检查用户名和邮箱是否已被其他用户使用
    if (username || email) {
      const [existing]: any = await pool.execute(
        'SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?',
        [username || target.username, email || target.email, id]
      )
      
      if (existing.length > 0) {
        return res.status(400).json({
          code: 400,
          message: '用户名或邮箱已被其他用户使用'
        })
      }
    }
    
    // 更新用户信息
    await pool.execute(
      'UPDATE users SET username = ?, email = ?, nickname = ?, tenant_id = ?, role = ?, status = ?, updated_at = NOW() WHERE id = ?',
      [
        username || target.username,
        email || target.email,
        nickname || target.nickname,
        tenant_id !== undefined ? tenant_id : target.tenant_id,
        role || target.role,
        status !== undefined ? status : target.status,
        id
      ]
    )
    
    res.json({
      code: 0,
      message: '用户更新成功'
    })
  } catch (error) {
    console.error('Update user error:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 删除用户
export const deleteUser = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params
    const currentUser = req.user!
    
    // 查询目标用户
    const [targetUser]: any = await pool.execute(
      'SELECT * FROM users WHERE id = ?',
      [id]
    )
    
    if (targetUser.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      })
    }
    
    const target = targetUser[0]
    
    // 不能删除自己
    if (target.id === currentUser.id) {
      return res.status(400).json({
        code: 400,
        message: '不能删除自己'
      })
    }
    
    // 权限检查：普通管理员只能删除自己租户下的用户
    if (currentUser.role === 'admin' && currentUser.tenant_id && target.tenant_id !== currentUser.tenant_id) {
      return res.status(403).json({
        code: 403,
        message: '只能删除本租户下的用户'
      })
    }
    
    // 权限检查：不能删除超级管理员
    if (target.role === 'super_admin' && currentUser.role !== 'super_admin') {
      return res.status(403).json({
        code: 403,
        message: '权限不足'
      })
    }
    
    // 删除用户（级联删除相关数据）
    await pool.execute('DELETE FROM users WHERE id = ?', [id])
    
    res.json({
      code: 0,
      message: '用户删除成功'
    })
  } catch (error) {
    console.error('Delete user error:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}

// 重置用户密码
export const resetUserPassword = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params
    const { newPassword } = req.body
    const currentUser = req.user!
    
    if (!newPassword) {
      return res.status(400).json({
        code: 400,
        message: '新密码不能为空'
      })
    }
    
    // 查询目标用户
    const [targetUser]: any = await pool.execute(
      'SELECT * FROM users WHERE id = ?',
      [id]
    )
    
    if (targetUser.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      })
    }
    
    const target = targetUser[0]
    
    // 权限检查
    if (currentUser.role === 'admin' && currentUser.tenant_id && target.tenant_id !== currentUser.tenant_id) {
      return res.status(403).json({
        code: 403,
        message: '只能重置本租户下用户的密码'
      })
    }
    
    if (target.role === 'super_admin' && currentUser.role !== 'super_admin') {
      return res.status(403).json({
        code: 403,
        message: '权限不足'
      })
    }
    
    // 加密新密码
    const salt = await bcrypt.genSalt(10)
    const hashedPassword = await bcrypt.hash(newPassword, salt)
    
    // 更新密码
    await pool.execute(
      'UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?',
      [hashedPassword, id]
    )
    
    res.json({
      code: 0,
      message: '密码重置成功'
    })
  } catch (error) {
    console.error('Reset password error:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
}
