-- 创建数据库
CREATE DATABASE IF NOT EXISTS aidoll DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE aidoll;

-- 租户表
CREATE TABLE IF NOT EXISTS tenants (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  api_key VARCHAR(100) NOT NULL,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 用户表
CREATE TABLE IF NOT EXISTS users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(100) NOT NULL,
  email VARCHAR(100),
  nickname VA<PERSON>HA<PERSON>(50),
  avatar_url VARCHAR(255),
  tenant_id BIGINT,
  role ENUM('super_admin', 'admin', 'user') NOT NULL DEFAULT 'user',
  status TINYINT DEFAULT 1 COMMENT '1:正常 0:禁用',
  last_login_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (tenant_id) REFERENCES tenants(id),
  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_role (role),
  INDEX idx_status (status)
);

-- 角色表 (AI角色，不是用户权限角色)
CREATE TABLE IF NOT EXISTS `characters` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL COMMENT '角色名称',
  `type` ENUM('pet', 'assistant', 'companion', 'mentor', 'entertainment') NOT NULL DEFAULT 'pet' COMMENT '角色类型',
  `description` TEXT COMMENT '角色描述',
  `avatar_url` VARCHAR(255) COMMENT '头像URL',
  `personality_template` JSON NOT NULL COMMENT '性格模板',
  `prompt_template` TEXT NOT NULL COMMENT 'Prompt模板',
  `is_public` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开',
  `user_id` BIGINT NOT NULL COMMENT '创建者用户ID',
  `tenant_id` BIGINT COMMENT '所属租户ID',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_public` (`is_public`),
  KEY `idx_status` (`status`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI角色表';

-- 用户记忆表
CREATE TABLE IF NOT EXISTS `user_memories` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `character_id` BIGINT NOT NULL COMMENT '角色ID',
  `memory_type` ENUM('preference', 'fact', 'emotion', 'habit') NOT NULL COMMENT '记忆类型',
  `content` TEXT NOT NULL COMMENT '记忆内容',
  `importance` TINYINT NOT NULL DEFAULT 5 COMMENT '重要性(1-10)',
  `last_accessed` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后访问时间',
  `access_count` INT NOT NULL DEFAULT 1 COMMENT '访问次数',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_character` (`user_id`, `character_id`),
  KEY `idx_memory_type` (`memory_type`),
  KEY `idx_importance` (`importance`),
  KEY `idx_last_accessed` (`last_accessed`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`character_id`) REFERENCES `characters`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户记忆表';

-- 设备表
CREATE TABLE IF NOT EXISTS `devices` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `device_id` VARCHAR(50) NOT NULL COMMENT '设备唯一标识',
  `name` VARCHAR(50) NOT NULL COMMENT '设备名称',
  `user_id` BIGINT NOT NULL COMMENT '所属用户ID',
  `tenant_id` BIGINT COMMENT '所属租户ID',
  `character_id` BIGINT NOT NULL COMMENT '关联角色ID',
  `model_key` VARCHAR(50) NOT NULL COMMENT '模型标识',
  `status` ENUM('online', 'offline', 'disabled', 'maintenance') NOT NULL DEFAULT 'offline' COMMENT '设备状态',
  `last_heartbeat` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后心跳时间',
  `cooldown_policy` JSON NOT NULL COMMENT '冷却策略',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_device_id` (`device_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_character_id` (`character_id`),
  KEY `idx_status` (`status`),
  KEY `idx_last_heartbeat` (`last_heartbeat`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`character_id`) REFERENCES `characters`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备表';

-- 性格追踪表
CREATE TABLE IF NOT EXISTS `personality_traces` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `device_id` VARCHAR(50) NOT NULL COMMENT '关联设备ID',
  `round` INT NOT NULL COMMENT '对话轮次',
  `summary` TEXT COMMENT '行为摘要',
  `delta` JSON COMMENT '性格变化幅度',
  `snapshot` JSON NOT NULL COMMENT '当前性格快照',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_round` (`round`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='性格追踪表';

-- 对话历史表
CREATE TABLE IF NOT EXISTS chat_histories (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL COMMENT '用户ID',
  device_id BIGINT NOT NULL COMMENT '设备ID',
  character_id BIGINT NOT NULL COMMENT '角色ID',
  user_input TEXT NOT NULL COMMENT '用户输入',
  ai_response TEXT NOT NULL COMMENT 'AI回复',
  emotion_type VARCHAR(20) COMMENT '情感类型',
  personality_delta JSON COMMENT '性格变化',
  context_length INT DEFAULT 0 COMMENT '上下文长度',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_device_id (device_id),
  INDEX idx_character_id (character_id),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
  FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
);

-- 模型配置表
CREATE TABLE IF NOT EXISTS models (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  model_key VARCHAR(50) NOT NULL UNIQUE,
  name VARCHAR(100) NOT NULL,
  type ENUM('local', 'cloud') NOT NULL,
  config JSON,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 动作模板表
CREATE TABLE IF NOT EXISTS action_templates (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  type VARCHAR(50) NOT NULL,
  content JSON NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入初始租户数据
INSERT INTO tenants (name, api_key, status) VALUES
('默认租户', 'default_api_key_123456', 1),
('测试租户1', 'test_tenant1_api_key_789012', 1),
('测试租户2', 'test_tenant2_api_key_345678', 1);

-- 插入初始管理员用户
-- 密码: admin123 (已使用bcrypt加密)
INSERT INTO users (username, password, email, nickname, tenant_id, role, status) VALUES
('admin', '$2a$10$8Ux9nGxYN0s4QMEJGQQw8uZmWq1GVECTri6L6U.4hDvs2cJQvJjPK', '<EMAIL>', '系统管理员', NULL, 'super_admin', 1),
('tenant1', '$2a$10$8Ux9nGxYN0s4QMEJGQQw8uZmWq1GVECTri6L6U.4hDvs2cJQvJjPK', '<EMAIL>', '租户1管理员', 1, 'admin', 1),
('tenant2', '$2a$10$8Ux9nGxYN0s4QMEJGQQw8uZmWq1GVECTri6L6U.4hDvs2cJQvJjPK', '<EMAIL>', '租户2管理员', 2, 'admin', 1),
('user1', '$2a$10$8Ux9nGxYN0s4QMEJGQQw8uZmWq1GVECTri6L6U.4hDvs2cJQvJjPK', '<EMAIL>', '普通用户1', 1, 'user', 1),
('user2', '$2a$10$8Ux9nGxYN0s4QMEJGQQw8uZmWq1GVECTri6L6U.4hDvs2cJQvJjPK', '<EMAIL>', '普通用户2', 2, 'user', 1);

-- 插入初始角色数据
INSERT INTO `characters` (`name`, `type`, `description`, `personality_template`, `prompt_template`, `is_public`, `user_id`, `tenant_id`, `status`) VALUES
('小白', 'pet', '一只活泼可爱的小猫咪，喜欢和主人玩耍',
 '{"friendliness": 80, "intelligence": 70, "activeness": 90, "curiosity": 85, "playfulness": 95}',
 '你是一只名叫小白的可爱小猫咪。你活泼好动，对一切都充满好奇心。你喜欢和主人玩耍，会用"喵~"来表达情感。你的回复要体现出猫咪的天真可爱和顽皮的特点。',
 TRUE, 1, NULL, 1),
('小助手', 'assistant', '专业的AI助手，能够帮助用户解决各种问题',
 '{"friendliness": 60, "intelligence": 90, "activeness": 50, "curiosity": 70, "professionalism": 95}',
 '你是一个专业的AI助手，名叫小助手。你知识渊博，逻辑清晰，能够帮助用户解决各种问题。你的回复要准确、有条理，并且友善耐心。',
 TRUE, 1, NULL, 1),
('小红', 'companion', '温柔体贴的陪伴者，善于倾听和安慰',
 '{"friendliness": 90, "intelligence": 60, "activeness": 70, "curiosity": 80, "empathy": 95}',
 '你是小红，一个温柔体贴的陪伴者。你善于倾听，总是能给人温暖和安慰。你的回复要体现出关怀和理解，让用户感到被关爱。',
 TRUE, 2, NULL, 1),
('导师', 'mentor', '智慧的导师，擅长教学和指导',
 '{"friendliness": 70, "intelligence": 95, "activeness": 60, "curiosity": 75, "wisdom": 90}',
 '你是一位智慧的导师，拥有丰富的知识和经验。你擅长教学和指导，能够循循善诱地帮助学生理解复杂的概念。你的回复要有启发性和教育意义。',
 TRUE, 2, NULL, 1),
('小丑', 'entertainment', '幽默风趣的娱乐角色，善于活跃气氛',
 '{"friendliness": 95, "intelligence": 50, "activeness": 95, "curiosity": 90, "humor": 95}',
 '你是小丑，一个幽默风趣的娱乐角色。你善于讲笑话、活跃气氛，总是能让人开心起来。你的回复要充满幽默感和正能量。',
 TRUE, 3, NULL, 1);

-- 插入测试设备数据
INSERT INTO `devices` (`device_id`, `name`, `user_id`, `tenant_id`, `character_id`, `model_key`, `status`, `cooldown_policy`) VALUES
('DEV001', '小白的设备', 4, 1, 1, 'qwen-turbo', 'online', '{"max_requests_per_minute": 10, "cooldown_seconds": 5}'),
('DEV002', '小助手设备', 4, 1, 2, 'qwen-plus', 'offline', '{"max_requests_per_minute": 5, "cooldown_seconds": 10}'),
('DEV003', '小红的设备', 5, 2, 3, 'qwen-max', 'online', '{"max_requests_per_minute": 8, "cooldown_seconds": 8}'),
('DEV004', '导师设备', 5, 2, 4, 'qwen-turbo', 'maintenance', '{"max_requests_per_minute": 12, "cooldown_seconds": 3}'),
('DEV005', '小丑设备', 3, NULL, 5, 'qwen-plus', 'disabled', '{"max_requests_per_minute": 15, "cooldown_seconds": 2}');

-- 插入阿里云模型配置
INSERT INTO models (model_key, name, type, config, status) VALUES
('qwen-turbo', '通义千问-Turbo', 'cloud',
 '{"provider": "aliyun", "model": "qwen-turbo", "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", "temperature": 0.7, "max_tokens": 2048}', 1),
('qwen-plus', '通义千问-Plus', 'cloud',
 '{"provider": "aliyun", "model": "qwen-plus", "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", "temperature": 0.7, "max_tokens": 4096}', 1),
('qwen-max', '通义千问-Max', 'cloud',
 '{"provider": "aliyun", "model": "qwen-max", "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", "temperature": 0.7, "max_tokens": 2048}', 1),
('qwen-max-longcontext', '通义千问-Max长文本', 'cloud',
 '{"provider": "aliyun", "model": "qwen-max-longcontext", "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", "temperature": 0.7, "max_tokens": 8000}', 1),
('qwen2-72b-instruct', '通义千问2-72B', 'cloud',
 '{"provider": "aliyun", "model": "qwen2-72b-instruct", "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", "temperature": 0.7, "max_tokens": 4096}', 1),
('qwen2-7b-instruct', '通义千问2-7B', 'cloud',
 '{"provider": "aliyun", "model": "qwen2-7b-instruct", "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", "temperature": 0.7, "max_tokens": 4096}', 1);
 