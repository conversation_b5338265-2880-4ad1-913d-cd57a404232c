import express from 'express'
import cors from 'cors'
import dotenv from 'dotenv'
import { Server } from 'http'
import { Server as SocketIOServer } from 'socket.io'

// 路由导入
import authRoutes from './routes/auth'
import userRoutes from './routes/user'
import adminRoutes from './routes/admin'
import deviceRoutes from './routes/device'
import characterRoutes from './routes/character'
import modelRoutes from './routes/model'
import tenantRoutes from './routes/tenant'
import actionRoutes from './routes/action'
import chatRoutes from './routes/chat'
import mqttRoutes from './routes/mqtt'
import vendorRoutes from './routes/vendor'
import aliyunModelRoutes from './routes/aliyunModel'
import memoryRoutes from './routes/memory'
import enhancedChatRoutes from './routes/enhancedChat'
import enhancedDeviceRoutes from './routes/enhancedDevice'
import externalMqttRoutes from './routes/externalMqtt'
import deviceVendorRoutes from './routes/deviceVendor'
const promptRoutes = require('./routes/promptRoutes')
const vendorMqttRoutes = require('./routes/vendorMqttRoutes')

// 日志广播服务
const logBroadcastService = require('./services/logBroadcastService')

// MQTT服务导入
import { ExternalMqttService } from './services/externalMqttService'

dotenv.config()

const app = express()
const port = Number(process.env.PORT || '3000')
const host = process.env.HOST || '0.0.0.0' // 监听所有网络接口

// 中间件
app.use(cors({
  origin: '*', // 允许所有来源访问
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}))
app.use(express.json())
app.use(express.urlencoded({ extended: true }))

// 路由
app.use('/api/auth', authRoutes)
app.use('/api/user', userRoutes)
app.use('/api/admin', adminRoutes)
app.use('/api/devices', deviceRoutes)
app.use('/api/characters', characterRoutes)
app.use('/api/models', modelRoutes)
app.use('/api/tenants', tenantRoutes)
app.use('/api/actions', actionRoutes)
app.use('/api/chat', chatRoutes)
app.use('/api/mqtt', mqttRoutes)
app.use('/api/vendor', vendorRoutes)
app.use('/api/aliyun-model', aliyunModelRoutes)
app.use('/api/memory', memoryRoutes)
app.use('/api/enhanced-chat', enhancedChatRoutes)
app.use('/api/enhanced-devices', enhancedDeviceRoutes)
app.use('/api/external-mqtt', externalMqttRoutes)
app.use('/api/device-vendors', deviceVendorRoutes)

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: require('../package.json').version
  })
})
app.use('/api/vendor-mqtt', vendorMqttRoutes)
app.use('/api/ai', promptRoutes)

// 启动简化MQTT服务 - 已暂时禁用，现在使用MQTT客户端模式连接厂商服务器
// const mqttService = new SimpleMQTTService()

// 错误处理
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack)
  res.status(500).json({
    code: 500,
    message: '服务器内部错误'
  })
})

const server: Server = app.listen(port, host, () => {
  const addr = server.address()
  const actualHost = typeof addr === 'string' ? addr : addr?.address || host
  const actualPort = typeof addr === 'string' ? port : addr?.port || port
  console.log(`HTTP服务器运行在 http://${actualHost}:${actualPort}`)
  console.log('AI娃娃后台管理系统 - 后端服务已启动')

  // 启动外部MQTT服务
  const externalMqttService = new ExternalMqttService()
  console.log('📡 外部MQTT服务已启动，连接到厂商MQTT服务器')
})

// 初始化Socket.IO
const io = new SocketIOServer(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
})

// 设置日志广播服务
logBroadcastService.setIO(io)

// Socket.IO连接处理
io.on('connection', (socket) => {
  console.log(`🔗 前端客户端连接: ${socket.id}`)
  
  // 发送最近的日志给新连接的客户端
  const recentLogs = logBroadcastService.getRecentLogs(null, 50)
  socket.emit('recent-logs', recentLogs)
  
  // 处理客户端请求历史日志
  socket.on('request-logs', (data) => {
    const { vendorId, limit } = data
    const logs = logBroadcastService.getRecentLogs(vendorId, limit || 100)
    socket.emit('logs-response', logs)
  })
  
  // 处理清空日志请求
  socket.on('clear-logs', (data) => {
    const { vendorId } = data
    logBroadcastService.clearLogs(vendorId)
  })
  
  socket.on('disconnect', () => {
    console.log(`📴 前端客户端断开: ${socket.id}`)
  })
})

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('正在关闭服务器...')
  server.close(async () => {
    // MQTT服务已禁用，不需要关闭
    // await mqttService.close()
    console.log('服务器已关闭')
    process.exit(0)
  })
})

process.on('SIGTERM', async () => {
  console.log('正在关闭服务器...')
  server.close(async () => {
    // MQTT服务已禁用，不需要关闭
    // await mqttService.close()
    console.log('服务器已关闭')
    process.exit(0)
  })
}) 
 