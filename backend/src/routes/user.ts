import { Router } from 'express'
import { auth } from '../middleware/auth'
import {
  register,
  login,
  getUserInfo,
  updateUserInfo,
  changePassword
} from '../controllers/user'

const router = Router()

// 公开路由
router.post('/register', register)
router.post('/login', login)

// 需要认证的路由
router.get('/info', auth, getUserInfo)
router.put('/info', auth, updateUserInfo)
router.put('/password', auth, changePassword)

export default router
