import { Router } from 'express'
import { auth } from '../middleware/auth'
import { checkDeviceAccess, requireAdmin } from '../middleware/permission'
import {
  getMqttStatus,
  getDeviceMqttLogs,
  sendTestMessage,
  getDeviceHardwareStatus,
  getMqttStats,
  getMqttMessageStats
} from '../controllers/externalMqtt'

const router = Router()

// 所有路由都需要认证
router.use(auth)

// 获取MQTT连接状态
router.get('/status', getMqttStatus)

// 获取MQTT统计信息
router.get('/stats', getMqttStats)

// 获取MQTT消息统计
router.get('/message-stats', getMqttMessageStats)

// 获取设备MQTT活动日志
router.get('/devices/:device_id/logs', checkDeviceAccess, getDeviceMqttLogs)

// 获取设备硬件状态
router.get('/devices/:device_id/hardware', checkDeviceAccess, getDeviceHardwareStatus)

// 发送测试消息（需要管理员权限）
router.post('/test-message', requireAdmin, sendTestMessage)

export default router
