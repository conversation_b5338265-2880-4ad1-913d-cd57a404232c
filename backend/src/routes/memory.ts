import { Router } from 'express'
import { auth } from '../middleware/auth'
import {
  getMemories,
  addMemory,
  searchMemories,
  getMemoryStats,
  cleanupMemories,
  getMemoryTypes,
  importMemories
} from '../controllers/memory'

const router = Router()

// 公开路由
router.get('/types', getMemoryTypes)

// 需要认证的路由
router.get('/', auth, getMemories)
router.post('/', auth, addMemory)
router.get('/search', auth, searchMemories)
router.get('/stats', auth, getMemoryStats)
router.post('/cleanup', auth, cleanupMemories)
router.post('/import', auth, importMemories)

export default router
