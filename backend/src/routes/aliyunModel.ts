import { Router } from 'express'
import { auth } from '../middleware/auth'
import {
  testAliyunModel,
  getSupportedModels,
  configureAliyunModel,
  testCharacterChat,
  estimateTokens,
  batchHealthCheck
} from '../controllers/aliyunModel'

const router = Router()

// 公开路由
router.get('/supported-models', getSupportedModels)
router.post('/estimate-tokens', estimateTokens)

// 需要认证的路由
router.post('/test', auth, testAliyunModel)
router.post('/configure', auth, configureAliyunModel)
router.post('/test-character', auth, testCharacterChat)
router.get('/health-check', auth, batchHealthCheck)

export default router
