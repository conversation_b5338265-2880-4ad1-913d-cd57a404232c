import { Router } from 'express'
import { auth } from '../middleware/auth'
import {
  checkAdminPermission,
  getUserList,
  createUser,
  updateUser,
  deleteUser,
  resetUserPassword
} from '../controllers/admin'

const router = Router()

// 所有管理员路由都需要认证和管理员权限
router.use(auth)
router.use(checkAdminPermission)

// 用户管理
router.get('/users', getUserList)
router.post('/users', createUser)
router.put('/users/:id', updateUser)
router.delete('/users/:id', deleteUser)
router.put('/users/:id/password', resetUserPassword)

export default router
