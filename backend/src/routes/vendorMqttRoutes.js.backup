const express = require('express');
const fs = require('fs');
const path = require('path');
const { VendorMqttClientService } = require('../services/vendorMqttClientService');
const logBroadcastService = require('../services/logBroadcastService');

const router = express.Router();

// 存储活跃的MQTT客户端实例
const activeClients = new Map();

/**
 * 获取厂商MQTT配置
 */
router.get('/config', (req, res) => {
    try {
        const configPath = path.join(__dirname, '../../config/vendor-mqtt-config.json');
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        
        // 隐藏敏感信息
        const safeConfig = {
            ...config,
            vendors: Object.fromEntries(
                Object.entries(config.vendors).map(([vendorId, vendorConfig]) => [
                    vendorId,
                    {
                        ...vendorConfig,
                        password: '*'.repeat(vendorConfig.password.length)
                    }
                ])
            )
        };
        
        res.json({
            code: 0,
            message: '获取配置成功',
            data: safeConfig
        });
    } catch (error) {
        console.error('获取厂商MQTT配置失败:', error);
        res.status(500).json({
            code: 500,
            message: '获取配置失败'
        });
    }
});

/**
 * 获取所有厂商的连接状态
 */
router.get('/status', (req, res) => {
    const startTime = Date.now();
    const requestId = Math.random().toString(36).substr(2, 9);
    console.log(`📈 [${new Date().toISOString()}] [${requestId}] 收到状态请求 - IP: ${req.ip}`);
    
    try {
        const configPath = path.join(__dirname, '../../config/vendor-mqtt-config.json');
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        
        const vendorStatuses = Object.keys(config.vendors).map(vendorId => {
            const client = activeClients.get(vendorId);
            return {
                vendorId,
                name: config.vendor_info?.[vendorId]?.name || vendorId,
                connected: client ? client.isConnected : false,
                clientId: config.vendors[vendorId].clientId,
                lastActivity: client ? new Date().toISOString() : null,
                status: client ? (client.isConnected ? 'online' : 'offline') : 'stopped'
            };
        });
        
        const responseData = {
            code: 0,
            message: '获取状态成功',
            data: {
                server: `${config.mqtt.host}:${config.mqtt.port}`,
                vendors: vendorStatuses,
                total: vendorStatuses.length,
                online: vendorStatuses.filter(v => v.connected).length
            }
        };
        
        const duration = Date.now() - startTime;
        console.log(`✅ [${new Date().toISOString()}] [${requestId}] 状态请求处理完成 - 耗时: ${duration}ms`);
        
        res.json(responseData);
    } catch (error) {
        const duration = Date.now() - startTime;
        console.error(`❌ [${new Date().toISOString()}] [${requestId}] 获取厂商MQTT状态失败 - 耗时: ${duration}ms`, error);
        res.status(500).json({
            code: 500,
            message: '获取状态失败'
        });
    }
});

/**
 * 启动指定厂商的MQTT客户端
 */
router.post('/vendor/:vendorId/start', async (req, res) => {
    try {
        const { vendorId } = req.params;
        
        // 检查是否已经启动
        if (activeClients.has(vendorId)) {
            const client = activeClients.get(vendorId);
            if (client.isConnected) {
                return res.json({
                    code: 200,
                    message: '客户端已经在运行中',
                    data: { vendorId, status: 'already_running' }
                });
            }
        }
        
        // 创建新的客户端实例
        const client = new VendorMqttClientService(vendorId);
        await client.connect();
        
        // 存储客户端实例
        activeClients.set(vendorId, client);
        
        res.json({
            code: 0,
            message: '启动成功',
            data: {
                vendorId,
                status: 'started',
                connectionInfo: client.getStatus()
            }
        });
    } catch (error) {
        console.error(`启动厂商 ${req.params.vendorId} 客户端失败:`, error);
        res.status(500).json({
            code: 500,
            message: '启动失败: ' + error.message
        });
    }
});

/**
 * 停止指定厂商的MQTT客户端
 */
router.post('/vendor/:vendorId/stop', (req, res) => {
    try {
        const { vendorId } = req.params;
        
        const client = activeClients.get(vendorId);
        if (!client) {
            return res.json({
                code: 200,
                message: '客户端未运行',
                data: { vendorId, status: 'not_running' }
            });
        }
        
        // 断开连接
        client.disconnect();
        activeClients.delete(vendorId);
        
        res.json({
            code: 0,
            message: '停止成功',
            data: { vendorId, status: 'stopped' }
        });
    } catch (error) {
        console.error(`停止厂商 ${req.params.vendorId} 客户端失败:`, error);
        res.status(500).json({
            code: 500,
            message: '停止失败: ' + error.message
        });
    }
});

/**
 * 重启指定厂商的MQTT客户端
 */
router.post('/vendor/:vendorId/restart', async (req, res) => {
    try {
        const { vendorId } = req.params;
        
        // 先停止
        const client = activeClients.get(vendorId);
        if (client) {
            client.disconnect();
            activeClients.delete(vendorId);
        }
        
        // 等待一秒
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 重新启动
        const newClient = new VendorMqttClientService(vendorId);
        await newClient.connect();
        activeClients.set(vendorId, newClient);
        
        res.json({
            code: 0,
            message: '重启成功',
            data: {
                vendorId,
                status: 'restarted',
                connectionInfo: newClient.getStatus()
            }
        });
    } catch (error) {
        console.error(`重启厂商 ${req.params.vendorId} 客户端失败:`, error);
        res.status(500).json({
            code: 500,
            message: '重启失败: ' + error.message
        });
    }
});

/**
 * 获取指定厂商的详细信息
 */
router.get('/vendor/:vendorId/info', (req, res) => {
    try {
        const { vendorId } = req.params;
        const configPath = path.join(__dirname, '../../config/vendor-mqtt-config.json');
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        
        const vendorConfig = config.vendors[vendorId];
        const vendorInfo = config.vendor_info?.[vendorId];
        const client = activeClients.get(vendorId);
        
        if (!vendorConfig) {
            return res.status(404).json({
                code: 404,
                message: '厂商不存在'
            });
        }
        
        res.json({
            code: 0,
            message: '获取厂商信息成功',
            data: {
                vendorId,
                name: vendorInfo?.name || vendorId,
                status: vendorInfo?.status || 'unknown',
                devices: vendorInfo?.devices || [],
                config: {
                    username: vendorConfig.username,
                    password: '*'.repeat(vendorConfig.password.length),
                    clientId: vendorConfig.clientId
                },
                connection: client ? {
                    connected: client.isConnected,
                    ...client.getStatus()
                } : null
            }
        });
    } catch (error) {
        console.error('获取厂商信息失败:', error);
        res.status(500).json({
            code: 500,
            message: '获取厂商信息失败'
        });
    }
});

/**
 * 获取系统概览信息
 */
router.get('/overview', (req, res) => {
    const startTime = Date.now();
    const requestId = Math.random().toString(36).substr(2, 9);
    console.log(`📊 [${new Date().toISOString()}] [${requestId}] 收到概览请求 - IP: ${req.ip}`);
    
    try {
        const configPath = path.join(__dirname, '../../config/vendor-mqtt-config.json');
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        
        const totalVendors = Object.keys(config.vendors).length;
        const onlineVendors = Array.from(activeClients.values()).filter(client => client.isConnected).length;
        const totalDevices = Object.values(config.vendor_info || {})
            .reduce((sum, info) => sum + (info.devices?.length || 0), 0);
        
        const responseData = {
            code: 0,
            message: '获取概览成功',
            data: {
                server: {
                    host: config.mqtt.host,
                    port: config.mqtt.port,
                    protocol: config.mqtt.protocol || 'mqtt'
                },
                statistics: {
                    totalVendors,
                    onlineVendors,
                    offlineVendors: totalVendors - onlineVendors,
                    totalDevices,
                    connectionRate: totalVendors > 0 ? Math.round((onlineVendors / totalVendors) * 100) : 0
                },
                vendors: Object.keys(config.vendors).map(vendorId => {
                    const client = activeClients.get(vendorId);
                    return {
                        vendorId,
                        name: config.vendor_info?.[vendorId]?.name || vendorId,
                        connected: client ? client.isConnected : false,
                        devices: config.vendor_info?.[vendorId]?.devices?.length || 0
                    };
                })
            }
        };
        
        const duration = Date.now() - startTime;
        console.log(`✅ [${new Date().toISOString()}] [${requestId}] 概览请求处理完成 - 耗时: ${duration}ms`);
        
        res.json(responseData);
    } catch (error) {
        const duration = Date.now() - startTime;
        console.error(`❌ [${new Date().toISOString()}] [${requestId}] 获取系统概览失败 - 耗时: ${duration}ms`, error);
        res.status(500).json({
            code: 500,
            message: '获取概览失败'
        });
    }
});

/**
 * 测试厂商连接
 */
router.post('/vendor/:vendorId/test', async (req, res) => {
    try {
        const { vendorId } = req.params;
        const configPath = path.join(__dirname, '../../config/vendor-mqtt-config.json');
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        
        const vendorConfig = config.vendors[vendorId];
        if (!vendorConfig) {
            return res.status(404).json({
                code: 404,
                message: '厂商配置不存在'
            });
        }
        
        // 创建临时客户端进行测试
        const testClient = new VendorMqttClientService(vendorId);
        
        try {
            await testClient.connect();
            const status = testClient.getStatus();
            testClient.disconnect();
            
            res.json({
                code: 0,
                message: '连接测试成功',
                data: {
                    vendorId,
                    testResult: 'success',
                    connectionTime: new Date().toISOString(),
                    status
                }
            });
        } catch (testError) {
            res.json({
                code: 1,
                message: '连接测试失败',
                data: {
                    vendorId,
                    testResult: 'failed',
                    error: testError.message
                }
            });
        }
    } catch (error) {
        console.error('测试厂商连接失败:', error);
        res.status(500).json({
            code: 500,
            message: '测试失败: ' + error.message
        });
    }
});

// 获取历史日志
router.get('/logs/:vendorId?', (req, res) => {
    const requestId = Math.random().toString(36).substring(7);
    const requestStart = Date.now();
    const clientIP = req.ip || req.connection.remoteAddress;
    
    console.log(`📊 [${requestId}] 日志查询请求 - IP: ${clientIP}`);
    
    try {
        const { vendorId } = req.params;
        const { limit = 100 } = req.query;
        
        const logs = logBroadcastService.getRecentLogs(vendorId, parseInt(limit));
        const elapsed = Date.now() - requestStart;
        
        console.log(`✅ [${requestId}] 日志查询成功 - 耗时: ${elapsed}ms, 日志数: ${logs.length}`);
        
        res.json({
            success: true,
            data: {
                logs: logs,
                total: logs.length,
                vendorId: vendorId || 'all'
            }
        });
    } catch (error) {
        const elapsed = Date.now() - requestStart;
        console.error(`❌ [${requestId}] 日志查询失败 - 耗时: ${elapsed}ms, 错误:`, error.message);
        
        res.status(500).json({
            success: false,
            message: '获取日志失败',
            error: error.message
        });
    }
});

// 清空日志
router.delete('/logs/:vendorId?', (req, res) => {
    const requestId = Math.random().toString(36).substring(7);
    const requestStart = Date.now();
    const clientIP = req.ip || req.connection.remoteAddress;
    
    console.log(`📊 [${requestId}] 清空日志请求 - IP: ${clientIP}`);
    
    try {
        const { vendorId } = req.params;
        
        logBroadcastService.clearLogs(vendorId);
        const elapsed = Date.now() - requestStart;
        
        console.log(`✅ [${requestId}] 清空日志成功 - 耗时: ${elapsed}ms, 厂商: ${vendorId || '全部'}`);
        
        res.json({
            success: true,
            message: vendorId ? `厂商 ${vendorId} 的日志已清空` : '所有日志已清空'
        });
    } catch (error) {
        const elapsed = Date.now() - requestStart;
        console.error(`❌ [${requestId}] 清空日志失败 - 耗时: ${elapsed}ms, 错误:`, error.message);
        
        res.status(500).json({
            success: false,
            message: '清空日志失败',
            error: error.message
        });
    }
});

module.exports = router; 
