import express from 'express'
import { auth } from '../middleware/auth'
import {
  getCharacters,
  getCharacterById,
  createCharacter,
  updateCharacter,
  deleteCharacter,
  cloneCharacter,
  getCharacterTypes,
  getPersonalityTemplates
} from '../controllers/character'

const router = express.Router()

// 获取角色列表
router.get('/', auth, getCharacters)

// 获取角色类型列表
router.get('/types', getCharacterTypes)

// 获取性格模板列表
router.get('/personality-templates', getPersonalityTemplates)

// 获取单个角色
router.get('/:id', auth, getCharacterById)

// 创建角色
router.post('/', auth, createCharacter)

// 更新角色
router.put('/:id', auth, updateCharacter)

// 删除角色
router.delete('/:id', auth, deleteCharacter)

// 复制角色
router.post('/:id/clone', auth, cloneCharacter)

export default router 
