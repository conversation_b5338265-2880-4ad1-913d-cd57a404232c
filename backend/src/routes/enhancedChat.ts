import { Router } from 'express'
import { auth } from '../middleware/auth'
import {
  sendMessage,
  getChatHistory,
  clearContext,
  batchSendMessages,
  getChatStats,
  exportChatHistory
} from '../controllers/enhancedChat'

const router = Router()

// 所有路由都需要认证
router.use(auth)

// 发送消息
router.post('/send', sendMessage)

// 批量发送消息
router.post('/batch-send', batchSendMessages)

// 获取对话历史
router.get('/history/:device_id', getChatHistory)

// 获取对话统计
router.get('/stats/:device_id', getChatStats)

// 导出对话历史
router.get('/export/:device_id', exportChatHistory)

// 清除对话上下文
router.post('/clear-context', clearContext)

export default router
