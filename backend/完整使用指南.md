# 多用户AI对话平台完整使用指南

## 项目概述

这是一个功能完整的多用户AI对话平台，支持：
- 多用户注册和权限管理
- 阿里云通义千问大模型集成
- 自定义AI角色创建和管理
- 长期记忆系统
- 外部MQTT设备对接
- 厂商设备管理

## 快速开始

### 1. 环境准备

**系统要求：**
- Node.js 16.0+
- MySQL 8.0+
- Redis 6.0+
- 阿里云通义千问API访问权限

### 2. 安装和配置

#### 方式一：Docker部署（推荐）

```bash
# 1. 克隆项目
cd backend

# 2. 运行系统检查
node system-check.js

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置阿里云API密钥等信息

# 4. 使用部署脚本
chmod +x deploy.sh
./deploy.sh build    # 构建镜像
./deploy.sh start    # 启动服务

# 5. 查看服务状态
./deploy.sh logs     # 查看日志
```

#### 方式二：本地开发部署

```bash
# 1. 克隆项目
cd backend

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库、Redis、阿里云API等信息

# 4. 初始化数据库
mysql -u root -p < src/config/init.sql

# 5. 启动服务
npm run dev
```

### 3. 验证安装

访问 `http://localhost:3000` 确认服务启动成功。

## 功能使用指南

### 1. 用户管理

#### 默认账号
- **超级管理员**: `admin` / `admin123`
- **租户管理员**: `tenant1` / `admin123`
- **普通用户**: `user1` / `admin123`

#### 用户注册
```bash
curl -X POST http://localhost:3000/api/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "password": "password123",
    "email": "<EMAIL>",
    "nickname": "新用户"
  }'
```

#### 用户登录
```bash
curl -X POST http://localhost:3000/api/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user1",
    "password": "admin123"
  }'
```

### 2. 阿里云模型配置

#### 配置API密钥
```bash
curl -X POST http://localhost:3000/api/aliyun-model/configure \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "model_key": "qwen-turbo",
    "name": "通义千问-Turbo",
    "model": "qwen-turbo",
    "api_key": "YOUR_ALIYUN_API_KEY",
    "temperature": 0.7,
    "max_tokens": 2048
  }'
```

#### 测试模型连接
```bash
curl -X POST http://localhost:3000/api/aliyun-model/test \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "api_key": "YOUR_ALIYUN_API_KEY",
    "model": "qwen-turbo",
    "test_message": "你好"
  }'
```

### 3. 角色管理

#### 创建AI角色
```bash
curl -X POST http://localhost:3000/api/characters \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "小助手",
    "type": "assistant",
    "description": "友善的AI助手",
    "personality_template": {
      "friendliness": 90,
      "intelligence": 85,
      "activeness": 70,
      "curiosity": 75,
      "humor": 60,
      "empathy": 80
    },
    "prompt_template": "你是一个友善的AI助手，请用温和的语气回复用户。",
    "is_public": false
  }'
```

#### 获取角色列表
```bash
curl -X GET "http://localhost:3000/api/characters?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. 设备管理

#### 创建设备
```bash
curl -X POST http://localhost:3000/api/enhanced-devices \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "device_id": "DOLL_001",
    "name": "ABC玩具公司智能娃娃",
    "character_id": 1,
    "model_key": "qwen-turbo"
  }'
```

#### 获取设备列表
```bash
curl -X GET "http://localhost:3000/api/enhanced-devices?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 5. 对话功能

#### 发送消息
```bash
curl -X POST http://localhost:3000/api/enhanced-chat/send \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "device_id": "DOLL_001",
    "message": "你好，今天天气怎么样？"
  }'
```

#### 获取对话历史
```bash
curl -X GET "http://localhost:3000/api/enhanced-chat/history/DOLL_001?page=1&limit=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 6. 记忆管理

#### 添加记忆
```bash
curl -X POST http://localhost:3000/api/memory \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "character_id": 1,
    "memory_type": "preference",
    "content": "用户喜欢喝咖啡",
    "importance": 7
  }'
```

#### 搜索记忆
```bash
curl -X GET "http://localhost:3000/api/memory/search?character_id=1&query=咖啡&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 7. MQTT设备对接

#### 创建厂商
```bash
curl -X POST http://localhost:3000/api/device-vendors \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "vendor_id": "MY_VENDOR",
    "name": "我的设备厂商",
    "description": "智能设备制造商",
    "contact_info": {
      "email": "<EMAIL>",
      "phone": "+86-************"
    }
  }'
```

#### 获取MQTT状态
```bash
curl -X GET http://localhost:3000/api/external-mqtt/status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 管理员功能

### 1. 用户管理

#### 获取用户列表
```bash
curl -X GET "http://localhost:3000/api/admin/users?page=1&limit=10" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"
```

#### 创建用户
```bash
curl -X POST http://localhost:3000/api/admin/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -d '{
    "username": "newuser",
    "password": "password123",
    "email": "<EMAIL>",
    "nickname": "新用户",
    "role": "user"
  }'
```

#### 重置用户密码
```bash
curl -X PUT http://localhost:3000/api/admin/users/1/password \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -d '{
    "newPassword": "newpassword123"
  }'
```

## 权限说明

### 用户角色

1. **super_admin (超级管理员)**
   - 可以管理所有用户、租户、角色、设备
   - 可以访问所有数据
   - 可以创建管理员用户

2. **admin (租户管理员)**
   - 可以管理本租户下的用户
   - 可以访问本租户下的所有数据
   - 不能创建管理员用户

3. **user (普通用户)**
   - 只能管理自己的数据
   - 可以创建角色、设备
   - 可以使用公开角色

### 数据隔离

- 普通用户只能访问自己创建的数据
- 租户管理员可以访问本租户下所有数据
- 超级管理员可以访问所有数据
- 公开角色可以被所有用户使用

## 监控和维护

### 1. 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

### 2. 健康检查

```bash
# 检查服务状态
curl http://localhost:3000/health

# 检查MQTT连接状态
curl -X GET http://localhost:3000/api/external-mqtt/status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 检查阿里云模型状态
curl -X GET http://localhost:3000/api/aliyun-model/health-check \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. 性能监控

- API响应时间
- 数据库连接数
- Redis内存使用
- MQTT连接状态
- 阿里云API调用次数

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证连接参数
   - 检查防火墙设置

2. **Redis连接失败**
   - 检查Redis服务状态
   - 验证密码配置
   - 检查端口占用

3. **阿里云API调用失败**
   - 验证API Key有效性
   - 检查网络连接
   - 确认模型权限

4. **MQTT连接失败**
   - 检查MQTT服务器状态
   - 验证认证信息
   - 确认网络连接

### 调试技巧

1. 启用详细日志
2. 使用API测试工具
3. 检查数据库数据
4. 监控系统资源

## 扩展开发

### 1. 添加新的AI模型

1. 在 `modelService.ts` 中添加新的模型提供商
2. 实现相应的API调用逻辑
3. 更新数据库模型配置
4. 添加测试用例

### 2. 自定义记忆提取

1. 修改 `memoryService.ts` 中的 `analyzeTextForMemories` 方法
2. 添加新的记忆类型
3. 更新数据库枚举
4. 调整前端界面

### 3. 扩展MQTT功能

1. 添加新的消息类型
2. 实现自定义硬件控制
3. 扩展设备状态管理
4. 优化消息处理性能

## 总结

本平台提供了完整的多用户AI对话解决方案，包括：
- 用户管理和权限控制
- AI模型集成和管理
- 角色定制和记忆系统
- 设备管理和MQTT对接
- 监控和维护工具

通过本指南，您可以快速部署和使用平台的所有功能，为用户提供个性化的AI对话体验。
