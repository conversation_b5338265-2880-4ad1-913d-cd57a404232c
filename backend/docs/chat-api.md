# 对话API接口文档

## 概述

对话API是AI对话玩偶系统的核心接口，用于处理用户与玩偶设备的对话交互。该接口支持多轮对话、性格养成、动作生成等功能。

## 基础信息

- **基础URL**: `http://localhost:3000/api/chat`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

## 接口列表

### 1. 对话请求

**接口地址**: `POST /chat`

**功能描述**: 处理用户对话请求，返回AI回复和动作指令

**请求参数**:

```json
{
  "device_id": "string",     // 设备ID（必填）
  "user_input": "string",    // 用户输入内容（必填）
  "api_key": "string",       // API密钥（可选，用于验证）
  "timestamp": 1234567890,   // 时间戳（可选）
  "signature": "string"      // 签名（可选）
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "对话成功",
  "data": {
    "text": "你好！我是你的AI伙伴，很高兴和你聊天！",
    "actions": [
      {
        "type": "expression",
        "content": {
          "type": "smile",
          "intensity": 0.8,
          "duration": 3000
        },
        "duration": 3000,
        "priority": 1
      },
      {
        "type": "light",
        "content": {
          "pattern": "rainbow",
          "brightness": 0.8,
          "speed": "fast",
          "duration": 3000
        },
        "duration": 3000,
        "priority": 3
      }
    ],
    "emotion": "happy",
    "personality_delta": {
      "friendliness": 1.5,
      "activeness": 1.2
    },
    "context_length": 15,
    "timestamp": 1640995200000
  }
}
```

**错误响应**:

```json
{
  "code": 400,
  "message": "缺少必要参数: device_id 或 user_input"
}
```

```json
{
  "code": 404,
  "message": "设备不存在"
}
```

```json
{
  "code": 429,
  "message": "请求过于频繁，请稍后再试"
}
```

### 2. 获取对话历史

**接口地址**: `GET /history/:device_id`

**功能描述**: 获取指定设备的对话历史记录

**路径参数**:
- `device_id`: 设备ID

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页条数（默认20）

**响应示例**:

```json
{
  "code": 0,
  "message": "获取对话历史成功",
  "data": {
    "items": [
      {
        "id": 1,
        "device_id": "DEV001",
        "user_input": "你好",
        "ai_response": "你好！很高兴见到你！",
        "emotion_type": "happy",
        "personality_delta": {
          "friendliness": 1.2
        },
        "context_length": 3,
        "created_at": "2024-01-01T12:00:00.000Z"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 20
  }
}
```

### 3. 清除对话上下文

**接口地址**: `DELETE /context/:device_id`

**功能描述**: 清除指定设备的对话上下文缓存

**路径参数**:
- `device_id`: 设备ID

**响应示例**:

```json
{
  "code": 0,
  "message": "清除对话上下文成功"
}
```

### 4. 获取设备状态

**接口地址**: `GET /status/:device_id`

**功能描述**: 获取指定设备的详细状态信息

**路径参数**:
- `device_id`: 设备ID

**响应示例**:

```json
{
  "code": 0,
  "message": "获取设备状态成功",
  "data": {
    "device": {
      "id": 1,
      "device_id": "DEV001",
      "name": "小助手",
      "status": "online",
      "last_heartbeat": "2024-01-01T12:00:00.000Z",
      "character_type": "assistant",
      "model_key": "gpt-3.5-turbo"
    },
    "personality": {
      "currentPersonality": {
        "friendliness": 75,
        "intelligence": 90,
        "activeness": 65
      },
      "currentRound": 15,
      "recentChanges": [...],
      "totalChanges": 15
    },
    "context_length": 30,
    "is_in_cooldown": false
  }
}
```

## 动作类型说明

### 表情动作 (expression)
```json
{
  "type": "expression",
  "content": {
    "type": "smile|sad|angry|surprised|neutral",
    "intensity": 0.0-1.0,
    "duration": 1000-5000
  }
}
```

### 手势动作 (gesture)
```json
{
  "type": "gesture",
  "content": {
    "type": "wave|hug|point|nod",
    "intensity": 0.0-1.0,
    "duration": 1000-5000
  }
}
```

### 灯效动作 (light)
```json
{
  "type": "light",
  "content": {
    "pattern": "rainbow|breathing|flash|sparkle|solid",
    "color": "red|blue|green|yellow|white",
    "brightness": 0.0-1.0,
    "speed": "fast|medium|slow",
    "duration": 1000-5000
  }
}
```

### 音效动作 (sound)
```json
{
  "type": "sound",
  "content": {
    "type": "laugh|cry|chime|neutral",
    "volume": 0.0-1.0,
    "duration": 1000-3000
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 认证失败 |
| 404 | 资源不存在 |
| 429 | 请求频率超限 |
| 500 | 服务器内部错误 |

## 使用示例

### JavaScript示例

```javascript
// 发送对话请求
const response = await fetch('http://localhost:3000/api/chat/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    device_id: 'DEV001',
    user_input: '你好，今天天气怎么样？',
    api_key: 'your_api_key'
  })
})

const result = await response.json()
console.log('AI回复:', result.data.text)
console.log('动作指令:', result.data.actions)
```

### Python示例

```python
import requests

# 发送对话请求
response = requests.post('http://localhost:3000/api/chat/chat', json={
    'device_id': 'DEV001',
    'user_input': '你好，今天天气怎么样？',
    'api_key': 'your_api_key'
})

result = response.json()
print('AI回复:', result['data']['text'])
print('动作指令:', result['data']['actions'])
```

## 注意事项

1. **设备状态**: 确保设备状态为 `online` 或 `offline`，其他状态无法处理对话
2. **请求频率**: 系统会根据设备的冷却策略限制请求频率
3. **上下文长度**: 系统会自动维护对话上下文，最多保留20轮对话
4. **性格养成**: 每次对话都会影响设备的性格参数，实现性格养成
5. **动作生成**: 系统会根据对话内容和性格参数自动生成相应的动作指令
6. **API密钥**: 建议在生产环境中使用API密钥进行认证

## 更新日志

- v1.0.0: 初始版本，支持基础对话功能
- v1.1.0: 添加性格养成和动作生成功能
- v1.2.0: 优化上下文管理和性能 
