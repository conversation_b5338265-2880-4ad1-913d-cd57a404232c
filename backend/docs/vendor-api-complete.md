# AI对话玩偶厂商接口文档

## 📋 概述

本文档为硬件厂商提供完整的AI对话玩偶系统接入指南。系统支持**HTTP REST API**和**MQTT协议**两种接入方式，厂商可根据技术栈选择合适的协议。

### 🎯 核心功能
- **语音对话处理** - 接收用户语音转文字，返回AI回复
- **硬件控制指令** - 控制屏幕表情和三舵机动作
- **设备状态管理** - 心跳监控、状态查询、故障诊断
- **实时通信** - 支持低延迟的双向消息传递

### 🔄 工作流程
```mermaid
graph LR
    A[用户语音] --> B[厂商设备]
    B --> C[语音转文字]
    C --> D[AI系统]
    D --> E[生成回复+动作]
    E --> F[厂商设备]
    F --> G[语音播放]
    F --> H[屏幕表情]
    F --> I[舵机动作]
```

---

## 🌐 HTTP REST API

### 基础信息

| 项目 | 值 |
|------|-----|
| 基础URL | `https://api.ai-doll.cn` |
| 协议 | HTTPS |
| 认证方式 | API Key (Header) |
| 数据格式 | JSON |
| 超时时间 | 10秒 |

### 认证

所有HTTP请求需要在Header中携带API密钥：

```http
Authorization: Bearer {YOUR_API_KEY}
Content-Type: application/json
```

### 接口列表

#### 1. 对话回调接口

**接口地址**: `POST /api/vendor/callback`

**功能描述**: 接收厂商抄送的用户对话，返回AI回复和硬件控制指令

**请求参数**:
```json
{
  "device_id": "DOLL_001",              // 必填，设备唯一标识
  "user_input": "你好，我今天心情不好",    // 必填，用户语音转文字结果
  "vendor_id": "ABC_TOYS",              // 必填，厂商标识
  "timestamp": 1640995200000,           // 必填，Unix时间戳（毫秒）
  "vendor_data": {                      // 可选，厂商特有数据
    "battery_level": 85,
    "wifi_strength": 0.9,
    "temperature": 28.5
  }
}
```

**响应格式**:
```json
{
  "device_id": "DOLL_001",
  "response_text": "哦，怎么了？想和我聊聊吗？",
  "screen": {
    "image_url": "https://assets.ai-doll.cn/emotions/sad.gif",
    "image_timer": 100,          // 每帧间隔时间(毫秒)
    "image_counter": 30,         // 总播放帧数
    "image_mode": "once"         // 播放模式: "once" | "loop"
  },
  "servos": {
    "left_ear": [-1, 0, 1, 0],   // 左耳动作序列
    "right_ear": [0, 0, 0, 0],   // 右耳动作序列  
    "tail": [-1, 0, -1, 0]       // 尾巴动作序列
  },
  "timestamp": 1640995200000
}
```

#### 2. 设备心跳接口

**接口地址**: `POST /api/vendor/heartbeat`

**功能描述**: 上报设备在线状态和健康信息

**请求参数**:
```json
{
  "device_id": "DOLL_001",      // 必填，设备唯一标识
  "vendor_id": "ABC_TOYS",      // 必填，厂商标识
  "status": "online",           // 可选，设备状态
  "battery_level": 90,          // 可选，电池电量(0-100)
  "wifi_strength": 0.95,        // 可选，WiFi信号强度(0-1)
  "temperature": 25.8           // 可选，设备温度
}
```

**响应格式**:
```json
{
  "code": 0,
  "message": "心跳更新成功",
  "server_time": "2024-01-01T12:00:00.000Z",
  "timestamp": 1640995200000
}
```

#### 3. 设备状态查询

**接口地址**: `GET /api/vendor/device/{device_id}/status`

**功能描述**: 查询指定设备的当前状态

**请求参数**: URL路径参数 `device_id`

**响应格式**:
```json
{
  "device_id": "DOLL_001",
  "status": "online",
  "battery_level": 90,
  "wifi_strength": 0.95,
  "last_heartbeat": "2024-01-01T12:00:00.000Z",
  "uptime": 86400000,          // 运行时间(毫秒)
  "character": {
    "name": "小美",
    "personality": "温柔体贴",
    "current_mood": "开心"
  }
}
```

#### 错误响应格式

```json
{
  "code": 400,
  "message": "错误描述",
  "error": "详细错误信息",
  "timestamp": 1640995200000
}
```

**常见错误码**:
- `400` - 请求参数错误
- `401` - 认证失败
- `404` - 设备不存在
- `429` - 请求过于频繁
- `500` - 服务器内部错误

---

## 📡 MQTT协议

### 连接信息

| 项目 | 值 |
|------|-----|
| 服务器地址 | `mqtt.ai-doll.cn` |
| 端口 | `1884` |
| 协议版本 | MQTT 3.1.1 |
| 认证方式 | Username/Password |
| 心跳间隔 | 60秒 |

### 连接认证

```json
{
  "host": "mqtt.ai-doll.cn",
  "port": 1884,
  "username": "vendor_{VENDOR_ID}",          // 厂商用户名
  "password": "{API_KEY}",                   // API密钥
  "clientId": "vendor_device_{DEVICE_ID}",   // 客户端ID
  "clean": true,
  "keepalive": 60
}
```

### 主题规范

#### 发布主题（设备→服务器）
- `vendor/callback` - 对话回调
- `vendor/heartbeat` - 设备心跳

#### 订阅主题（服务器→设备）
- `vendor/{device_id}/response` - AI回复响应
- `vendor/{device_id}/heartbeat_ack` - 心跳确认
- `vendor/{device_id}/error` - 错误消息

### MQTT接口定义

#### 1. 对话回调

**发布主题**: `vendor/callback`  
**QoS**: 1  
**方向**: 设备 → 服务器

**消息格式**:
```json
{
  "device_id": "DOLL_001",
  "user_input": "你好，我今天心情不好",
  "vendor_id": "ABC_TOYS",
  "device_name": "客厅玩偶",
  "timestamp": 1640995200000
}
```

**响应主题**: `vendor/{device_id}/response`  
**QoS**: 1  
**方向**: 服务器 → 设备

**响应格式**:
```json
{
  "device_id": "DOLL_001",
  "response_text": "哦，怎么了？想和我聊聊吗？",
  "screen": {
    "image_url": "https://assets.ai-doll.cn/emotions/sad.gif",
    "image_timer": 100,
    "image_counter": 30,
    "image_mode": "once"
  },
  "servos": {
    "left_ear": [-1, 0, 1, 0],
    "right_ear": [0, 0, 0, 0],
    "tail": [-1, 0, -1, 0]
  },
  "timestamp": 1640995200000
}
```

#### 2. 设备心跳

**发布主题**: `vendor/heartbeat`  
**QoS**: 0  
**方向**: 设备 → 服务器

**消息格式**:
```json
{
  "device_id": "DOLL_001",
  "vendor_id": "ABC_TOYS",
  "battery_level": 90,
  "wifi_strength": 0.95,
  "temperature": 25.8,
  "vendor_status": "normal",
  "timestamp": 1640995200000
}
```

**响应主题**: `vendor/{device_id}/heartbeat_ack`  
**QoS**: 0  
**方向**: 服务器 → 设备

**响应格式**:
```json
{
  "status": "ok",
  "server_time": "2024-01-01T12:00:00.000Z",
  "device_id": "DOLL_001"
}
```

#### 3. 错误消息

**主题**: `vendor/{device_id}/error`  
**QoS**: 1  
**方向**: 服务器 → 设备

**消息格式**:
```json
{
  "error": "处理失败",
  "message": "设备ID不存在",
  "code": 404,
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

---

## 🎭 硬件控制协议

### 屏幕控制

屏幕用于显示玩偶的表情动画，支持GIF格式。

**控制参数**:
```json
{
  "image_url": "string",        // 表情GIF文件URL
  "image_timer": "number",      // 每帧间隔时间(毫秒)
  "image_counter": "number",    // 总播放帧数
  "image_mode": "string"        // 播放模式: "once" | "loop"
}
```

**预设表情列表**:

| 表情 | 文件名 | 触发场景 | 参数示例 |
|------|--------|----------|----------|
| 开心 | `smile.gif` | 用户积极情绪 | `timer:100, counter:30, mode:once` |
| 难过 | `sad.gif` | 用户消极情绪 | `timer:150, counter:25, mode:once` |
| 惊讶 | `surprised.gif` | 用户表达惊讶 | `timer:80, counter:40, mode:once` |
| 中性 | `neutral.gif` | 普通对话 | `timer:200, counter:20, mode:loop` |
| 思考 | `thinking.gif` | AI思考时 | `timer:300, counter:15, mode:loop` |
| 眨眼 | `blink.gif` | 待机状态 | `timer:500, counter:10, mode:loop` |

### 舵机控制

三个舵机分别控制左耳、右耳、尾巴的动作。

**控制参数**:
```json
{
  "left_ear": "number[]",       // 左耳动作序列
  "right_ear": "number[]",      // 右耳动作序列
  "tail": "number[]"            // 尾巴动作序列
}
```

**舵机位置值**:
- `-1` - 向下/向左
- `0` - 中性位置
- `1` - 向上/向右

**预设动作组合**:

| 动作名称 | 场景 | 左耳 | 右耳 | 尾巴 | 说明 |
|---------|------|------|------|------|------|
| 欢呼 | 用户开心时 | `[1,0,1,0]` | `[1,0,1,0]` | `[1,0,1,0]` | 全部摆动表示兴奋 |
| 安慰 | 用户难过时 | `[0,0,0,0]` | `[0,0,0,0]` | `[-1,0,-1,0]` | 尾巴轻摆表示安慰 |
| 警觉 | 用户惊讶时 | `[1,1,1,1]` | `[1,1,1,1]` | `[0,0,0,0]` | 双耳竖起保持警觉 |
| 点头 | 普通对话 | `[0,0,0,0]` | `[0,0,0,0]` | `[0,1,0,-1]` | 尾巴摆动表示回应 |
| 待机 | 无交互时 | `[0,0,0,0]` | `[0,0,0,0]` | `[0,0,0,0]` | 保持中性位置 |

---

## 🔧 SDK与示例代码

### Python SDK示例

```python
import asyncio
import json
import paho.mqtt.client as mqtt
from typing import Dict, Callable

class AIDollVendorSDK:
    def __init__(self, config: Dict):
        self.device_id = config['device_id']
        self.vendor_id = config['vendor_id']
        self.api_key = config['api_key']
        
        # MQTT客户端
        self.client = mqtt.Client(f"vendor_device_{self.device_id}")
        self.client.username_pw_set(f"vendor_{self.vendor_id}", self.api_key)
        self.client.on_connect = self._on_connect
        self.client.on_message = self._on_message
        
        # 回调函数
        self.on_ai_response: Callable = None
        self.on_error: Callable = None
        
    async def connect(self):
        """连接MQTT服务器"""
        self.client.connect("mqtt.ai-doll.cn", 1884, 60)
        self.client.loop_start()
        
    def _on_connect(self, client, userdata, flags, rc):
        print(f"MQTT连接成功: {rc}")
        
        # 订阅响应主题
        topics = [
            f"vendor/{self.device_id}/response",
            f"vendor/{self.device_id}/heartbeat_ack",
            f"vendor/{self.device_id}/error"
        ]
        
        for topic in topics:
            client.subscribe(topic)
    
    def _on_message(self, client, userdata, msg):
        topic = msg.topic
        data = json.loads(msg.payload.decode())
        
        if topic.endswith('/response'):
            if self.on_ai_response:
                self.on_ai_response(data)
        elif topic.endswith('/error'):
            if self.on_error:
                self.on_error(data)
    
    def send_chat(self, user_input: str):
        """发送用户对话"""
        payload = {
            "device_id": self.device_id,
            "user_input": user_input,
            "vendor_id": self.vendor_id,
            "timestamp": int(time.time() * 1000)
        }
        
        self.client.publish("vendor/callback", json.dumps(payload))
    
    def send_heartbeat(self, battery_level: int = None, wifi_strength: float = None):
        """发送设备心跳"""
        payload = {
            "device_id": self.device_id,
            "vendor_id": self.vendor_id,
            "timestamp": int(time.time() * 1000)
        }
        
        if battery_level is not None:
            payload["battery_level"] = battery_level
        if wifi_strength is not None:
            payload["wifi_strength"] = wifi_strength
        
        self.client.publish("vendor/heartbeat", json.dumps(payload))

# 使用示例
async def main():
    config = {
        'device_id': 'DOLL_001',
        'vendor_id': 'ABC_TOYS',
        'api_key': 'your_api_key_here'
    }
    
    sdk = AIDollVendorSDK(config)
    
    # 设置回调函数
    def handle_ai_response(response):
        print(f"AI回复: {response['response_text']}")
        # 控制硬件
        control_screen(response['screen'])
        control_servos(response['servos'])
    
    sdk.on_ai_response = handle_ai_response
    
    # 连接并开始工作
    await sdk.connect()
    
    # 模拟用户对话
    sdk.send_chat("你好，今天天气真不错")
    
    # 定时发送心跳
    while True:
        sdk.send_heartbeat(battery_level=85, wifi_strength=0.9)
        await asyncio.sleep(30)  # 每30秒发送一次心跳

if __name__ == "__main__":
    asyncio.run(main())
```

### JavaScript SDK示例

```javascript
const mqtt = require('mqtt');
const axios = require('axios');

class AIDollVendorSDK {
    constructor(config) {
        this.deviceId = config.deviceId;
        this.vendorId = config.vendorId;
        this.apiKey = config.apiKey;
        this.useHTTP = config.useHTTP || false;
        
        if (!this.useHTTP) {
            this.setupMQTT();
        }
    }
    
    setupMQTT() {
        const clientId = `vendor_device_${this.deviceId}`;
        const username = `vendor_${this.vendorId}`;
        
        this.client = mqtt.connect('mqtt://mqtt.ai-doll.cn:1884', {
            clientId,
            username,
            password: this.apiKey,
            clean: true,
            keepalive: 60
        });
        
        this.client.on('connect', () => {
            console.log('MQTT连接成功');
            
            // 订阅响应主题
            const topics = [
                `vendor/${this.deviceId}/response`,
                `vendor/${this.deviceId}/heartbeat_ack`,
                `vendor/${this.deviceId}/error`
            ];
            
            topics.forEach(topic => {
                this.client.subscribe(topic);
            });
        });
        
        this.client.on('message', (topic, message) => {
            const data = JSON.parse(message.toString());
            
            if (topic.endsWith('/response')) {
                this.handleAIResponse(data);
            } else if (topic.endsWith('/error')) {
                this.handleError(data);
            }
        });
    }
    
    async sendChatHTTP(userInput) {
        try {
            const response = await axios.post('https://api.ai-doll.cn/api/vendor/callback', {
                device_id: this.deviceId,
                user_input: userInput,
                vendor_id: this.vendorId,
                timestamp: Date.now()
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            
            this.handleAIResponse(response.data);
        } catch (error) {
            this.handleError(error.response?.data || error.message);
        }
    }
    
    sendChatMQTT(userInput) {
        const payload = {
            device_id: this.deviceId,
            user_input: userInput,
            vendor_id: this.vendorId,
            timestamp: Date.now()
        };
        
        this.client.publish('vendor/callback', JSON.stringify(payload));
    }
    
    sendChat(userInput) {
        if (this.useHTTP) {
            return this.sendChatHTTP(userInput);
        } else {
            return this.sendChatMQTT(userInput);
        }
    }
    
    handleAIResponse(response) {
        console.log(`AI回复: ${response.response_text}`);
        
        // 控制屏幕显示
        if (response.screen) {
            this.controlScreen(response.screen);
        }
        
        // 控制舵机动作
        if (response.servos) {
            this.controlServos(response.servos);
        }
    }
    
    controlScreen(screenCmd) {
        console.log('屏幕控制:', screenCmd);
        // 在这里实现屏幕控制逻辑
        // displayGIF(screenCmd.image_url, screenCmd.image_timer, screenCmd.image_counter);
    }
    
    controlServos(servoCmd) {
        console.log('舵机控制:', servoCmd);
        // 在这里实现舵机控制逻辑
        // moveServo('left_ear', servoCmd.left_ear);
        // moveServo('right_ear', servoCmd.right_ear);
        // moveServo('tail', servoCmd.tail);
    }
}

// 使用示例
const config = {
    deviceId: 'DOLL_001',
    vendorId: 'ABC_TOYS',
    apiKey: 'your_api_key_here',
    useHTTP: false  // 设置为true使用HTTP，false使用MQTT
};

const sdk = new AIDollVendorSDK(config);

// 模拟用户对话
sdk.sendChat('你好，今天天气真不错');

// 定时心跳（仅MQTT模式）
if (!config.useHTTP) {
    setInterval(() => {
        const payload = {
            device_id: config.deviceId,
            vendor_id: config.vendorId,
            battery_level: Math.floor(Math.random() * 40) + 60,
            wifi_strength: Math.random() * 0.4 + 0.6,
            timestamp: Date.now()
        };
        
        sdk.client.publish('vendor/heartbeat', JSON.stringify(payload));
    }, 30000);
}
```

---

## 🚀 快速开始

### 1. 获取接入凭证

联系我们获取：
- 厂商ID (`vendor_id`)
- API密钥 (`api_key`)
- 设备ID模板

### 2. 创建测试设备

```bash
curl -X POST https://api.ai-doll.cn/api/devices \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "TEST_DOLL_001",
    "name": "测试玩偶",
    "character_type": "companion",
    "vendor_id": "YOUR_VENDOR_ID"
  }'
```

### 3. 测试对话功能

#### HTTP方式
```bash
curl -X POST https://api.ai-doll.cn/api/vendor/callback \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "TEST_DOLL_001",
    "user_input": "你好",
    "vendor_id": "YOUR_VENDOR_ID",
    "timestamp": 1640995200000
  }'
```

#### MQTT方式
```bash
# 发布消息到 vendor/callback 主题
mosquitto_pub -h mqtt.ai-doll.cn -p 1884 \
  -u vendor_YOUR_VENDOR_ID -P YOUR_API_KEY \
  -t vendor/callback \
  -m '{"device_id":"TEST_DOLL_001","user_input":"你好","vendor_id":"YOUR_VENDOR_ID","timestamp":1640995200000}'

# 订阅响应主题
mosquitto_sub -h mqtt.ai-doll.cn -p 1884 \
  -u vendor_YOUR_VENDOR_ID -P YOUR_API_KEY \
  -t vendor/TEST_DOLL_001/response
```

### 4. 运行测试脚本

我们提供了完整的测试脚本验证功能：

```bash
# HTTP测试
node test-vendor-adapter.js

# MQTT测试  
node test-vendor-mqtt.js
```

---

## 📊 监控与统计

### 性能指标

| 指标 | 目标值 | 说明 |
|------|--------|------|
| 响应时间 | < 1秒 | 对话处理延迟 |
| 可用性 | 99.9% | 服务可用时间 |
| 吞吐量 | 1000 QPS | 每秒请求处理能力 |
| 错误率 | < 0.1% | 请求失败比例 |

### 监控接口

#### 获取设备统计
```
GET /api/vendor/device/{device_id}/stats
```

响应示例：
```json
{
  "device_id": "DOLL_001",
  "stats": {
    "chat_count_today": 156,
    "avg_response_time": 0.8,
    "error_rate": 0.02,
    "uptime_percentage": 99.95,
    "last_24h_activity": [
      {"hour": "00", "chats": 5},
      {"hour": "01", "chats": 2},
      {"hour": "02", "chats": 1}
    ]
  }
}
```

---

## 🔒 安全规范

### API安全
- 所有HTTP接口必须使用HTTPS
- API密钥需要定期轮换（建议3个月）
- 实施请求频率限制（默认100次/分钟）
- 记录所有API调用日志

### MQTT安全
- 使用TLS加密连接（端口8883）
- 客户端证书认证
- 主题访问控制（ACL）
- 消息体加密（可选）

### 数据安全
- 用户对话数据仅用于AI处理
- 不存储敏感个人信息
- 遵循数据最小化原则
- 定期删除过期日志

---

## 🆘 常见问题

### Q1: 如何选择HTTP还是MQTT协议？
**A**: 
- **HTTP**: 适合简单集成，服务器压力小，调试方便
- **MQTT**: 适合低延迟要求，需要实时双向通信，设备资源有限

### Q2: 设备离线后如何处理消息？
**A**: 
- **HTTP**: 需要厂商自行实现重试机制
- **MQTT**: 支持消息持久化，设备重连后自动接收

### Q3: 如何处理网络不稳定的情况？
**A**:
- 实施指数退避重试策略
- 本地缓存重要指令
- 实现降级模式（本地语音库）
- 监控网络质量指标

### Q4: API调用频率限制是多少？
**A**: 默认限制：
- 对话接口：60次/分钟
- 心跳接口：2次/分钟  
- 查询接口：120次/分钟

### Q5: 如何获取表情资源文件？
**A**: 
- 标准表情包由我们提供CDN服务
- 自定义表情需要上传到指定存储
- 支持GIF、PNG、WebP格式
- 建议分辨率：128x128像素

---

## 📞 技术支持

### 联系方式
- **技术邮箱**: <EMAIL>
- **QQ技术群**: 123456789
- **工作时间**: 周一至周五 9:00-18:00
- **紧急联系**: 400-123-4567

### 文档更新
- 最新版本：v2.1.0
- 更新日期：2024-01-01
- 文档地址：https://docs.ai-doll.cn/vendor-api

### 开发工具
- **API测试工具**: Postman Collection
- **MQTT客户端**: MQTT.fx, MQTTX
- **SDK下载**: https://github.com/ai-doll/vendor-sdk

---

*本文档如有更新，以最新版本为准。如有疑问，请联系技术支持团队。* 
