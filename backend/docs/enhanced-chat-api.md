# 增强版对话API接口文档

## 概述

增强版对话API是AI对话玩偶系统的核心接口，支持接收和处理多种类型的数据，包括语音、传感器、环境信息、用户状态等，以提供更智能和个性化的对话体验。

## 基础信息

- **接口地址**: `POST /api/chat/chat`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

## 完整请求参数

### 基础参数（必填）

```json
{
  "device_id": "string",     // 设备ID（必填）
  "user_input": "string"     // 用户输入内容（必填）
}
```

### 认证参数（可选）

```json
{
  "api_key": "string",       // API密钥
  "timestamp": 1234567890,   // 时间戳
  "signature": "string"      // 签名
}
```

### 语音数据（可选）

```json
{
  "voice_data": {
    "audio_url": "https://example.com/audio.wav",  // 语音文件URL
    "audio_base64": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT",
    "duration": 3.5,                              // 音频时长(秒)
    "sample_rate": 16000,                         // 采样率
    "format": "wav",                              // 音频格式
    "language": "zh-CN",                          // 语音语言
    "confidence": 0.95,                           // 语音识别置信度
    "voice_emotion": "happy",                     // 语音情感分析结果
    "voice_tone": "high",                         // 语音语调(high/medium/low)
    "voice_speed": 1.2,                           // 语音速度
    "voice_volume": 0.8                           // 语音音量
  }
}
```

### 传感器数据（可选）

```json
{
  "sensor_data": {
    "temperature": 25.5,                          // 环境温度(摄氏度)
    "humidity": 60.2,                             // 环境湿度(%)
    "light_level": 800,                           // 光照强度(lux)
    "noise_level": 45.3,                          // 噪音水平(dB)
    "proximity": 0.8,                             // 距离传感器(0-1, 1表示最近)
    "touch_sensors": {                            // 触摸传感器状态
      "head": true,                               // 头部触摸
      "body": false,                              // 身体触摸
      "hands": true,                              // 手部触摸
      "feet": false                               // 脚部触摸
    },
    "motion_detected": true,                      // 运动检测
    "battery_level": 85,                          // 电池电量(%)
    "wifi_strength": 0.9                          // WiFi信号强度(0-1)
  }
}
```

### 环境信息（可选）

```json
{
  "environment": {
    "location": {                                 // 位置信息
      "latitude": 39.9042,                        // 纬度
      "longitude": 116.4074,                      // 经度
      "address": "北京市朝阳区",                   // 地址
      "room": "客厅"                              // 房间
    },
    "time": {                                     // 时间信息
      "timestamp": 1640995200000,                 // 时间戳
      "timezone": "Asia/Shanghai",                // 时区
      "is_daytime": true,                         // 是否白天
      "day_of_week": 1,                           // 星期几(0-6)
      "hour_of_day": 14                           // 小时(0-23)
    },
    "weather": {                                  // 天气信息
      "condition": "sunny",                       // 天气状况
      "temperature": 28.5,                        // 室外温度
      "humidity": 65.0,                           // 室外湿度
      "is_raining": false                         // 是否下雨
    },
    "ambient_light": 750,                         // 环境光照(lux)
    "ambient_noise": 42.1                         // 环境噪音(dB)
  }
}
```

### 用户状态信息（可选）

```json
{
  "user_context": {
    "user_id": "user_12345",                      // 用户ID
    "user_age": 8,                                // 用户年龄
    "user_gender": "female",                      // 用户性别
    "user_mood": "excited",                       // 用户当前心情
    "user_energy_level": 8,                       // 用户精力水平(1-10)
    "user_attention_level": 9,                    // 用户注意力水平(1-10)
    "user_interaction_history": {                 // 用户交互历史
      "total_interactions": 150,                  // 总交互次数
      "last_interaction_time": 1640995200000,     // 上次交互时间
      "favorite_topics": ["游戏", "故事", "音乐"],  // 喜欢的话题
      "avoided_topics": ["学习", "作业"]           // 避免的话题
    },
    "user_preferences": {                         // 用户偏好
      "preferred_voice_speed": 1.0,               // 偏好语音速度
      "preferred_voice_tone": "friendly",         // 偏好语音语调
      "preferred_interaction_style": "playful",   // 偏好交互风格
      "preferred_topics": ["游戏", "故事"]         // 偏好话题
    }
  }
}
```

### 设备状态信息（可选）

```json
{
  "device_context": {
    "device_mode": "normal",                      // 设备模式
    "device_battery": 85,                         // 设备电池电量(%)
    "device_volume": 0.7,                         // 设备音量(0-1)
    "device_brightness": 0.8,                     // 设备亮度(0-1)
    "device_network_status": "connected",         // 网络状态
    "device_error_codes": [],                     // 错误代码
    "device_features_enabled": [                  // 启用的功能
      "voice_recognition",
      "emotion_detection",
      "touch_sensing"
    ]
  }
}
```

### 交互上下文（可选）

```json
{
  "interaction_context": {
    "conversation_id": "conv_20240101_001",       // 对话会话ID
    "turn_number": 5,                             // 当前轮次
    "session_duration": 180,                      // 会话持续时间(秒)
    "previous_topics": ["天气", "游戏", "故事"],    // 之前讨论的话题
    "user_intent": "entertainment",               // 用户意图
    "conversation_flow": "casual",                // 对话流程状态
    "interruption_count": 0                       // 中断次数
  }
}
```

### 情感分析数据（可选）

```json
{
  "emotion_data": {
    "detected_emotions": {                        // 检测到的情感
      "primary_emotion": "happy",                 // 主要情感
      "secondary_emotions": ["excited", "curious"], // 次要情感
      "confidence_scores": {                      // 置信度分数
        "happy": 0.85,
        "excited": 0.72,
        "curious": 0.68
      }
    },
    "sentiment_score": 0.8,                       // 情感倾向分数(-1到1)
    "arousal_level": 7,                           // 唤醒水平(1-10)
    "valence_level": 8,                           // 效价水平(1-10)
    "emotional_intensity": 7.5                    // 情感强度(1-10)
  }
}
```

### 行为分析数据（可选）

```json
{
  "behavior_data": {
    "interaction_pattern": "active",              // 交互模式
    "response_time": 2.3,                         // 响应时间(秒)
    "engagement_level": 8,                        // 参与度水平(1-10)
    "attention_span": 15,                         // 注意力持续时间(分钟)
    "interaction_frequency": 5                    // 交互频率(次/小时)
  }
}
```

### 健康相关数据（可选）

```json
{
  "health_data": {
    "user_heart_rate": 85,                        // 心率(bpm)
    "user_stress_level": 3,                       // 压力水平(1-10)
    "user_sleep_quality": 8,                      // 睡眠质量(1-10)
    "user_activity_level": 7,                     // 活动水平(1-10)
    "user_energy_level": 8                        // 精力水平(1-10)
  }
}
```

### 学习相关数据（可选）

```json
{
  "learning_data": {
    "current_learning_topic": "数学",              // 当前学习主题
    "learning_progress": 75,                      // 学习进度(%)
    "difficulty_level": 3,                        // 难度等级(1-5)
    "learning_style": "visual",                   // 学习风格
    "knowledge_gaps": ["分数", "小数"]             // 知识空白
  }
}
```

### 游戏相关数据（可选）

```json
{
  "game_data": {
    "current_game": "数学冒险",                    // 当前游戏
    "game_score": 1250,                           // 游戏分数
    "game_level": 5,                              // 游戏等级
    "game_difficulty": "medium",                  // 游戏难度
    "game_mood": "excited"                        // 游戏心情
  }
}
```

### 自定义数据（可选）

```json
{
  "custom_data": {                                // 厂商自定义数据
    "brand_specific_feature": "value",
    "custom_analytics": {
      "feature_usage": "high",
      "user_satisfaction": 9.2
    }
  }
}
```

## 完整请求示例

```json
{
  "device_id": "DEVICE_001",
  "user_input": "你好，今天天气怎么样？",
  "api_key": "your_api_key_here",
  "timestamp": 1640995200000,
  "signature": "calculated_signature_here",
  "voice_data": {
    "audio_url": "https://example.com/audio.wav",
    "duration": 3.5,
    "confidence": 0.95,
    "voice_emotion": "happy",
    "voice_tone": "high"
  },
  "sensor_data": {
    "temperature": 25.5,
    "humidity": 60.2,
    "touch_sensors": {
      "head": true,
      "body": false
    },
    "battery_level": 85
  },
  "environment": {
    "location": {
      "latitude": 39.9042,
      "longitude": 116.4074,
      "room": "客厅"
    },
    "time": {
      "timestamp": 1640995200000,
      "is_daytime": true,
      "hour_of_day": 14
    },
    "weather": {
      "condition": "sunny",
      "temperature": 28.5,
      "is_raining": false
    }
  },
  "user_context": {
    "user_id": "user_12345",
    "user_age": 8,
    "user_mood": "excited",
    "user_energy_level": 8
  },
  "device_context": {
    "device_mode": "normal",
    "device_battery": 85,
    "device_volume": 0.7
  },
  "interaction_context": {
    "conversation_id": "conv_20240101_001",
    "turn_number": 5,
    "session_duration": 180
  },
  "emotion_data": {
    "detected_emotions": {
      "primary_emotion": "happy",
      "confidence_scores": {
        "happy": 0.85
      }
    },
    "sentiment_score": 0.8
  },
  "health_data": {
    "user_heart_rate": 85,
    "user_stress_level": 3,
    "user_energy_level": 8
  },
  "learning_data": {
    "current_learning_topic": "数学",
    "learning_progress": 75
  },
  "game_data": {
    "current_game": "数学冒险",
    "game_score": 1250,
    "game_level": 5
  }
}
```

## 响应示例

```json
{
  "code": 0,
  "message": "对话成功",
  "data": {
    "text": "你好！今天天气很好呢，阳光明媚的！我看到你在客厅，而且心情很兴奋的样子。要不要一起玩个游戏或者听个故事呢？",
    "actions": [
      {
        "type": "expression",
        "content": {
          "type": "smile",
          "intensity": 0.8,
          "duration": 3000
        },
        "duration": 3000,
        "priority": 1
      },
      {
        "type": "light",
        "content": {
          "pattern": "rainbow",
          "brightness": 0.8,
          "speed": "fast",
          "duration": 3000
        },
        "duration": 3000,
        "priority": 3
      },
      {
        "type": "gesture",
        "content": {
          "type": "wave",
          "intensity": 0.7,
          "duration": 2000
        },
        "duration": 2000,
        "priority": 2
      }
    ],
    "emotion": "happy",
    "personality_delta": {
      "friendliness": 1.5,
      "activeness": 1.2
    },
    "context_length": 15,
    "timestamp": 1640995200000
  }
}
```

## 数据处理说明

### 语音数据处理
- 系统会分析语音情感和语调信息
- 语音数据会增强用户输入的上下文理解
- 支持多种音频格式和采样率

### 传感器数据处理
- 触摸传感器会触发特殊的交互响应
- 环境数据会影响AI的回复策略
- 电池电量会影响设备的响应模式

### 环境信息处理
- 时间信息会影响AI的回复风格（白天/夜晚）
- 天气信息会融入对话内容
- 位置信息用于个性化服务

### 用户状态处理
- 用户年龄影响AI的回复复杂度
- 用户心情影响AI的情感表达
- 交互历史用于个性化推荐

### 设备状态处理
- 设备模式影响AI的行为模式
- 电池电量影响响应策略
- 网络状态影响功能可用性

## 使用建议

1. **渐进式集成**: 建议逐步添加数据字段，先使用基础参数，再逐步增加高级功能
2. **数据质量**: 确保传感器数据的准确性和实时性
3. **隐私保护**: 注意用户隐私数据的保护和处理
4. **性能优化**: 大量数据可能影响响应速度，建议根据实际需求选择必要字段
5. **错误处理**: 实现完善的错误处理机制，特别是传感器数据异常的情况

## 扩展功能

- **语音合成**: 可以根据情感数据调整TTS参数
- **动作生成**: 传感器数据可以触发更丰富的动作响应
- **个性化学习**: 学习数据可以用于自适应教育内容
- **健康监测**: 健康数据可以用于情绪调节建议
- **游戏集成**: 游戏数据可以用于游戏化学习体验 
