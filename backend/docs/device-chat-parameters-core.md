# device/chat 接口核心参数文档

## 📋 概述

`device/chat` 是AI对话玩偶系统的核心接口，专注于设备状态和用户交互信息，提供智能对话体验。

## 🔗 接口信息

- **主题**: `device/chat`
- **QoS**: 1 (确保可靠传递)
- **方向**: 设备 → 服务器
- **协议**: MQTT / HTTP POST

---

## 📝 核心参数列表

### 1. 基础信息 (必填)

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `device_id` | string | ✅ | 设备唯一标识符 | `"DEVICE_001"` |
| `user_input` | string | ✅ | 用户输入内容 | `"你好，今天天气怎么样？"` |

### 2. 设备状态信息 (推荐)

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `device_battery` | number | ❌ | 设备电池电量(%) | `85` |
| `device_volume` | number | ❌ | 设备音量(0-1) | `0.7` |
| `device_mode` | string | ❌ | 设备模式 | `"normal"`, `"sleep"`, `"game"` |
| `device_network_status` | string | ❌ | 网络状态 | `"connected"` |

### 3. 用户交互信息 (推荐)

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `user_age` | number | ❌ | 用户年龄 | `8` |
| `user_mood` | string | ❌ | 用户当前心情 | `"happy"`, `"sad"`, `"excited"` |
| `conversation_id` | string | ❌ | 对话会话ID | `"conv_001"` |
| `turn_number` | number | ❌ | 当前轮次 | `1` |

### 4. 触摸传感器 (可选)

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `touch_sensors.head` | boolean | ❌ | 头部触摸状态 | `true` |
| `touch_sensors.body` | boolean | ❌ | 身体触摸状态 | `false` |
| `touch_sensors.hands` | boolean | ❌ | 手部触摸状态 | `true` |

### 5. 环境信息 (可选)

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `is_daytime` | boolean | ❌ | 是否白天 | `true` |
| `room` | string | ❌ | 房间位置 | `"客厅"`, `"卧室"` |
| `weather_condition` | string | ❌ | 天气状况 | `"sunny"`, `"rainy"` |

---

## 📄 请求示例

### 最小请求 (只包含必填参数)
```json
{
  "device_id": "DEVICE_001",
  "user_input": "你好，今天天气怎么样？"
}
```

### 标准请求 (包含推荐参数)
```json
{
  "device_id": "DEVICE_001",
  "user_input": "你好，今天天气怎么样？我想听个故事",
  "device_battery": 85,
  "device_volume": 0.7,
  "device_mode": "normal",
  "device_network_status": "connected",
  "user_age": 8,
  "user_mood": "excited",
  "conversation_id": "conv_001",
  "turn_number": 1,
  "touch_sensors": {
    "head": true,
    "body": false,
    "hands": true
  },
  "is_daytime": true,
  "room": "客厅",
  "weather_condition": "sunny"
}
```

### 完整请求 (包含所有可选参数)
```json
{
  "device_id": "DEVICE_001",
  "user_input": "你好，今天天气怎么样？我想听个故事",
  "device_battery": 85,
  "device_volume": 0.7,
  "device_brightness": 0.8,
  "device_mode": "normal",
  "device_network_status": "connected",
  "user_age": 8,
  "user_gender": "female",
  "user_mood": "excited",
  "conversation_id": "conv_001",
  "turn_number": 1,
  "session_duration": 60,
  "touch_sensors": {
    "head": true,
    "body": false,
    "hands": true,
    "feet": false
  },
  "motion_detected": true,
  "is_daytime": true,
  "room": "客厅",
  "weather_condition": "sunny",
  "temperature": 25.5,
  "light_level": 800
}
```

---

## 📊 参数统计

### 总计参数数量
- **必填参数**: 2个
- **推荐参数**: 8个
- **可选参数**: 8个
- **总参数**: 18个

### 参数分类
- **基础信息**: 2个 (必填)
- **设备状态**: 4个 (推荐)
- **用户交互**: 4个 (推荐)
- **触摸传感器**: 3个 (可选)
- **环境信息**: 5个 (可选)

---

## 🎯 使用建议

### 1. 最小化实现
```json
{
  "device_id": "DEVICE_001",
  "user_input": "用户输入内容"
}
```

### 2. 推荐实现
添加设备状态和用户信息：
```json
{
  "device_id": "DEVICE_001",
  "user_input": "用户输入内容",
  "device_battery": 85,
  "device_mode": "normal",
  "user_age": 8,
  "user_mood": "happy",
  "conversation_id": "conv_001",
  "turn_number": 1
}
```

### 3. 增强实现
根据设备能力添加传感器数据：
```json
{
  "device_id": "DEVICE_001",
  "user_input": "用户输入内容",
  "device_battery": 85,
  "device_mode": "normal",
  "user_age": 8,
  "user_mood": "happy",
  "conversation_id": "conv_001",
  "turn_number": 1,
  "touch_sensors": {
    "head": true,
    "body": false,
    "hands": true
  },
  "is_daytime": true,
  "room": "客厅"
}
```

---

## ⚠️ 注意事项

1. **必填参数**: `device_id` 和 `user_input` 必须提供
2. **设备能力**: 根据实际设备能力选择性提供参数
3. **数据准确性**: 确保提供的数据准确可靠
4. **性能考虑**: 参数过多可能影响响应速度

---

## 🔄 响应格式

服务器会返回以下格式的响应：

```json
{
  "text": "AI回复内容",
  "actions": [
    {
      "type": "expression",
      "content": {
        "emotion": "happy",
        "intensity": 0.8
      }
    },
    {
      "type": "movement",
      "content": {
        "action": "nod",
        "duration": 1000
      }
    }
  ],
  "emotion": "happy",
  "timestamp": 1640995200000
}
```

---

*最后更新时间: 2024年1月1日* 
