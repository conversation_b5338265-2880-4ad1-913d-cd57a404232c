# MQTT API 接口文档

## 概述

AI对话玩偶系统支持MQTT协议，提供实时、高效的设备通信能力。MQTT协议特别适合IoT设备，具有低延迟、低带宽消耗、支持QoS等优势。

## 服务器配置

### 连接信息
- **TCP端口**: 1883 (默认)
- **WebSocket端口**: 8883 (默认)
- **协议**: MQTT 3.1.1 / MQTT 5.0
- **QoS支持**: 0, 1, 2
- **认证**: 用户名/密码认证

### 环境变量配置
```bash
MQTT_PORT=1883          # MQTT TCP端口
MQTT_WS_PORT=8883       # MQTT WebSocket端口
```

## 认证机制

### 连接参数
```javascript
{
  host: 'your-server.com',
  port: 1883,
  username: 'DEVICE_ID',     // 设备ID作为用户名
  password: 'your_password', // 设备密码
  clientId: 'unique_client_id',
  clean: true,
  reconnectPeriod: 1000,
  connectTimeout: 30000
}
```

### 认证流程
1. 设备使用设备ID作为用户名连接
2. 系统验证设备ID是否存在于数据库中
3. 验证成功后允许连接，失败则断开连接

## 主题结构

### 设备主题格式
```
device/{device_id}/{action}
```

### 广播主题格式
```
broadcast/{message_type}
```

## 消息类型

### 1. 对话消息

#### 发送对话请求
- **主题**: `device/chat`
- **QoS**: 1
- **方向**: 设备 → 服务器

```json
{
  "device_id": "DEVICE_001",
  "user_input": "你好，今天天气怎么样？",
  "voice_data": {
    "duration": 3.5,
    "confidence": 0.95,
    "voice_emotion": "happy",
    "voice_tone": "high"
  },
  "sensor_data": {
    "temperature": 25.5,
    "humidity": 60.2,
    "touch_sensors": {
      "head": true,
      "body": false
    }
  },
  "environment": {
    "time": {
      "timestamp": 1640995200000,
      "is_daytime": true
    },
    "weather": {
      "condition": "sunny"
    }
  },
  "user_context": {
    "user_age": 8,
    "user_mood": "excited"
  }
}
```

#### 接收对话回复
- **主题**: `device/{device_id}/server/chat/response`
- **QoS**: 1
- **方向**: 服务器 → 设备

```json
{
  "text": "你好！今天天气很好，阳光明媚。我看到你摸了我的头，是不是想和我一起玩呢？",
  "actions": [
    {
      "type": "expression",
      "content": {
        "emotion": "happy",
        "intensity": 0.8
      }
    },
    {
      "type": "movement",
      "content": {
        "action": "nod",
        "duration": 1000
      }
    },
    {
      "type": "light",
      "content": {
        "color": "warm_yellow",
        "brightness": 0.7,
        "pattern": "pulse"
      }
    }
  ],
  "emotion": "happy",
  "personality_delta": {
    "friendliness": 2,
    "activeness": 1
  },
  "context_length": 15,
  "timestamp": 1640995200000
}
```

### 2. 心跳消息

#### 发送心跳
- **主题**: `device/heartbeat`
- **QoS**: 0
- **方向**: 设备 → 服务器

```json
{
  "device_id": "DEVICE_001",
  "timestamp": 1640995200000,
  "battery_level": 85,
  "wifi_strength": 0.9,
  "status": "online"
}
```

### 3. 状态更新消息

#### 发送状态更新
- **主题**: `device/status`
- **QoS**: 1
- **方向**: 设备 → 服务器

```json
{
  "device_id": "DEVICE_001",
  "status": "online",
  "device_mode": "normal",
  "device_battery": 85,
  "device_volume": 0.7,
  "device_brightness": 0.8,
  "device_network_status": "connected",
  "device_error_codes": [],
  "device_features_enabled": [
    "voice_recognition",
    "emotion_detection",
    "touch_sensing"
  ]
}
```

### 4. 传感器数据消息

#### 发送传感器数据
- **主题**: `device/sensor`
- **QoS**: 0
- **方向**: 设备 → 服务器

```json
{
  "device_id": "DEVICE_001",
  "sensor_data": {
    "temperature": 25.5,
    "humidity": 60.2,
    "light_level": 800,
    "noise_level": 45.3,
    "proximity": 0.8,
    "touch_sensors": {
      "head": true,
      "body": false,
      "hands": true,
      "feet": false
    },
    "motion_detected": true,
    "battery_level": 85,
    "wifi_strength": 0.9
  }
}
```

### 5. 错误消息

#### 接收错误信息
- **主题**: `device/{device_id}/server/error`
- **QoS**: 1
- **方向**: 服务器 → 设备

```json
{
  "error": "处理对话失败",
  "message": "设备不存在或已离线",
  "timestamp": 1640995200000,
  "request_id": "req_12345"
}
```

### 6. 广播消息

#### 接收广播消息
- **主题**: `broadcast/{message_type}`
- **QoS**: 1
- **方向**: 服务器 → 所有设备

```json
{
  "type": "system_maintenance",
  "message": "系统将在10分钟后进行维护",
  "start_time": 1640995200000,
  "duration": 1800000,
  "affected_services": ["chat", "voice"]
}
```

## 客户端实现示例

### JavaScript/Node.js 客户端

```javascript
const mqtt = require('mqtt')

// 连接配置
const config = {
  host: 'your-server.com',
  port: 1883,
  username: 'DEVICE_001',
  password: 'your_password',
  clientId: 'device_001_client',
  clean: true
}

// 连接MQTT服务器
const client = mqtt.connect(config)

// 连接成功
client.on('connect', () => {
  console.log('MQTT连接成功')
  
  // 订阅回复主题
  client.subscribe('device/DEVICE_001/server/chat/response')
  client.subscribe('device/DEVICE_001/server/error')
  client.subscribe('broadcast/#')
})

// 接收消息
client.on('message', (topic, message) => {
  const data = JSON.parse(message.toString())
  
  if (topic.includes('server/chat/response')) {
    console.log('收到AI回复:', data.text)
    // 处理AI回复和动作
    handleAIResponse(data)
  } else if (topic.includes('server/error')) {
    console.error('服务器错误:', data.error)
  } else if (topic.includes('broadcast')) {
    console.log('广播消息:', data)
  }
})

// 发送对话请求
function sendChatRequest(userInput, sensorData) {
  const message = {
    device_id: 'DEVICE_001',
    user_input: userInput,
    sensor_data: sensorData,
    timestamp: Date.now()
  }
  
  client.publish('device/chat', JSON.stringify(message), { qos: 1 })
}

// 发送心跳
function sendHeartbeat() {
  const message = {
    device_id: 'DEVICE_001',
    timestamp: Date.now(),
    battery_level: getBatteryLevel(),
    wifi_strength: getWifiStrength()
  }
  
  client.publish('device/heartbeat', JSON.stringify(message), { qos: 0 })
}

// 定期发送心跳
setInterval(sendHeartbeat, 30000) // 每30秒发送一次心跳
```

### Python 客户端

```python
import paho.mqtt.client as mqtt
import json
import time

class MQTTDeviceClient:
    def __init__(self, device_id, server_host, server_port=1883):
        self.device_id = device_id
        self.client = mqtt.Client(client_id=f"{device_id}_client")
        self.client.username_pw_set(device_id, "your_password")
        
        # 设置回调函数
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        
        # 连接到服务器
        self.client.connect(server_host, server_port, 60)
        self.client.loop_start()
    
    def on_connect(self, client, userdata, flags, rc):
        print(f"MQTT连接成功，返回码: {rc}")
        
        # 订阅主题
        client.subscribe(f"device/{self.device_id}/server/chat/response")
        client.subscribe(f"device/{self.device_id}/server/error")
        client.subscribe("broadcast/#")
    
    def on_message(self, client, userdata, msg):
        try:
            data = json.loads(msg.payload.decode())
            
            if "server/chat/response" in msg.topic:
                print(f"收到AI回复: {data['text']}")
                self.handle_ai_response(data)
            elif "server/error" in msg.topic:
                print(f"服务器错误: {data['error']}")
            elif "broadcast" in msg.topic:
                print(f"广播消息: {data}")
        except json.JSONDecodeError:
            print("消息格式错误")
    
    def send_chat_request(self, user_input, sensor_data=None):
        message = {
            "device_id": self.device_id,
            "user_input": user_input,
            "sensor_data": sensor_data or {},
            "timestamp": int(time.time() * 1000)
        }
        
        self.client.publish("device/chat", json.dumps(message), qos=1)
    
    def send_heartbeat(self):
        message = {
            "device_id": self.device_id,
            "timestamp": int(time.time() * 1000),
            "battery_level": self.get_battery_level(),
            "wifi_strength": self.get_wifi_strength()
        }
        
        self.client.publish("device/heartbeat", json.dumps(message), qos=0)
    
    def handle_ai_response(self, data):
        # 处理AI回复和动作
        print(f"AI回复: {data['text']}")
        print(f"情感: {data['emotion']}")
        
        # 执行动作
        for action in data.get('actions', []):
            self.execute_action(action)
    
    def execute_action(self, action):
        action_type = action['type']
        content = action['content']
        
        if action_type == 'expression':
            self.set_expression(content['emotion'], content['intensity'])
        elif action_type == 'movement':
            self.perform_movement(content['action'], content['duration'])
        elif action_type == 'light':
            self.set_light(content['color'], content['brightness'], content['pattern'])
    
    def get_battery_level(self):
        # 获取电池电量
        return 85
    
    def get_wifi_strength(self):
        # 获取WiFi信号强度
        return 0.9
    
    def set_expression(self, emotion, intensity):
        print(f"设置表情: {emotion}, 强度: {intensity}")
    
    def perform_movement(self, action, duration):
        print(f"执行动作: {action}, 持续时间: {duration}ms")
    
    def set_light(self, color, brightness, pattern):
        print(f"设置灯光: 颜色={color}, 亮度={brightness}, 模式={pattern}")

# 使用示例
if __name__ == "__main__":
    client = MQTTDeviceClient("DEVICE_001", "your-server.com")
    
    # 发送对话请求
    client.send_chat_request("你好，今天天气怎么样？")
    
    # 定期发送心跳
    import threading
    def heartbeat_loop():
        while True:
            client.send_heartbeat()
            time.sleep(30)
    
    heartbeat_thread = threading.Thread(target=heartbeat_loop, daemon=True)
    heartbeat_thread.start()
    
    # 保持程序运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("程序退出")
```

## 最佳实践

### 1. 连接管理
- 使用唯一的clientId
- 实现自动重连机制
- 设置合理的连接超时时间

### 2. 消息质量
- 对话消息使用QoS 1确保可靠传递
- 心跳和传感器数据使用QoS 0减少网络开销
- 重要消息使用QoS 2确保只传递一次

### 3. 错误处理
- 监听连接错误和消息错误
- 实现重试机制
- 记录错误日志

### 4. 性能优化
- 合理设置心跳间隔（建议30-60秒）
- 批量发送传感器数据
- 使用压缩减少带宽消耗

### 5. 安全考虑
- 使用TLS加密传输
- 定期更新设备密码
- 验证消息来源

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证服务器地址和端口
   - 确认用户名和密码正确

2. **消息丢失**
   - 检查QoS设置
   - 确认网络稳定性
   - 查看服务器日志

3. **认证失败**
   - 确认设备ID存在于数据库
   - 检查密码是否正确
   - 验证设备状态

4. **性能问题**
   - 调整心跳间隔
   - 优化消息大小
   - 检查网络带宽

### 调试工具

1. **MQTT客户端工具**
   - MQTT Explorer
   - Mosquitto Client
   - HiveMQ Web Client

2. **日志查看**
   - 服务器日志
   - 客户端日志
   - 网络抓包

## 版本历史

- **v1.0.0**: 初始版本，支持基础MQTT通信
- **v1.1.0**: 添加WebSocket支持
- **v1.2.0**: 增强认证机制
- **v1.3.0**: 添加广播消息功能 
