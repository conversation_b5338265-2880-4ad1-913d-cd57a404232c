# AI对话玩偶MQTT API 完整接口文档

## 📋 目录
- [概述](#概述)
- [服务信息](#服务信息)
- [MQTT消息接口](#mqtt消息接口)
- [HTTP管理API](#http管理api)
- [连接配置](#连接配置)
- [客户端示例](#客户端示例)
- [测试方法](#测试方法)
- [错误码说明](#错误码说明)

---

## 📖 概述

AI对话玩偶系统支持MQTT协议，为IoT设备提供实时、高效的通信能力。本系统支持设备与AI的实时对话、传感器数据收集、设备状态监控等功能。

### 🎯 主要特性
- **实时对话**: 设备与AI的实时对话交互
- **多传感器支持**: 温度、湿度、触摸、运动等传感器数据
- **设备管理**: 设备注册、状态监控、心跳检测
- **动作控制**: 表情、动作、灯光、声音控制
- **广播消息**: 支持向所有设备发送广播消息
- **高并发**: 支持大量设备同时连接

---

## 🚀 服务信息

### 连接信息
- **MQTT TCP端口**: 1883 (默认)
- **MQTT WebSocket端口**: 8883 (默认)
- **HTTP管理API**: http://localhost:3000/api/mqtt
- **协议版本**: MQTT 3.1.1/5.0
- **QoS支持**: 0, 1, 2
- **认证方式**: 用户名/密码认证

### 环境变量配置
```bash
# MQTT配置
MQTT_PORT=1883          # MQTT TCP端口
MQTT_WS_PORT=8883       # MQTT WebSocket端口

# 服务器配置
PORT=3000               # HTTP API端口
HOST=0.0.0.0           # 监听地址
```

---

## 📡 MQTT消息接口

### 1. 对话消息

#### 发送对话请求
- **主题**: `device/chat`
- **QoS**: 1 (确保可靠传递)
- **方向**: 设备 → 服务器

```json
{
  "device_id": "DEVICE_001",
  "user_input": "你好，今天天气怎么样？",
  "voice_data": {
    "audio_url": "https://example.com/audio.wav",
    "audio_base64": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/e",
    "duration": 3.5,
    "sample_rate": 16000,
    "format": "wav",
    "language": "zh-CN",
    "confidence": 0.95,
    "voice_emotion": "happy",
    "voice_tone": "high",
    "voice_speed": 1.0,
    "voice_volume": 0.8
  },
  "sensor_data": {
    "temperature": 25.5,
    "humidity": 60.2,
    "light_level": 800,
    "noise_level": 45.3,
    "proximity": 0.8,
    "touch_sensors": {
      "head": true,
      "body": false,
      "hands": true,
      "feet": false
    },
    "motion_detected": true,
    "battery_level": 85,
    "wifi_strength": 0.9
  },
  "environment": {
    "location": {
      "latitude": 39.9042,
      "longitude": 116.4074,
      "address": "北京市朝阳区",
      "room": "客厅"
    },
    "time": {
      "timestamp": 1640995200000,
      "timezone": "Asia/Shanghai",
      "is_daytime": true,
      "day_of_week": 1,
      "hour_of_day": 14
    },
    "weather": {
      "condition": "sunny",
      "temperature": 28.5,
      "humidity": 65.0,
      "is_raining": false
    },
    "ambient_light": 750,
    "ambient_noise": 42.1
  },
  "user_context": {
    "user_id": "user_12345",
    "user_age": 8,
    "user_gender": "female",
    "user_mood": "excited",
    "user_energy_level": 8,
    "user_attention_level": 9,
    "user_interaction_history": {
      "total_interactions": 150,
      "last_interaction_time": 1640991600000,
      "favorite_topics": ["游戏", "故事", "音乐"],
      "avoided_topics": ["学习", "作业"]
    },
    "user_preferences": {
      "preferred_voice_speed": 1.0,
      "preferred_voice_tone": "friendly",
      "preferred_interaction_style": "playful",
      "preferred_topics": ["游戏", "故事"]
    }
  },
  "device_context": {
    "device_mode": "normal",
    "device_battery": 85,
    "device_volume": 0.7,
    "device_brightness": 0.8,
    "device_network_status": "connected",
    "device_error_codes": [],
    "device_features_enabled": ["voice_recognition", "emotion_detection", "touch_sensing"]
  },
  "interaction_context": {
    "conversation_id": "conv_001",
    "turn_number": 1,
    "session_duration": 60,
    "previous_topic": "天气",
    "user_intent": "询问天气",
    "conversation_flow": "greeting"
  },
  "emotion_data": {
    "detected_emotions": {
      "primary_emotion": "happy",
      "confidence_scores": {
        "happy": 0.85,
        "excited": 0.75,
        "calm": 0.15
      }
    },
    "sentiment_score": 0.8,
    "arousal_level": 0.7,
    "valence_level": 0.8
  },
  "behavior_data": {
    "interaction_pattern": "verbal",
    "response_time": 1200,
    "engagement_level": 0.9,
    "attention_duration": 45,
    "interaction_frequency": "high"
  },
  "health_data": {
    "user_heart_rate": 85,
    "user_stress_level": 3,
    "user_sleep_quality": 8,
    "user_activity_level": 7,
    "user_energy_level": 8
  },
  "learning_data": {
    "current_learning_topic": "故事",
    "learning_progress": 50,
    "difficulty_level": "medium",
    "learning_style": "visual",
    "knowledge_gaps": ["数学", "科学"]
  },
  "game_data": {
    "current_game": "故事时间",
    "game_score": 100,
    "game_level": 2,
    "game_difficulty": "medium",
    "game_mood": "excited"
  },
  "custom_data": {
    "device_specific": "custom_value",
    "user_specific": "user_preference",
    "session_specific": "session_data"
  }
}
```

#### 接收对话回复
- **主题**: `device/{device_id}/server/chat/response`
- **QoS**: 1
- **方向**: 服务器 → 设备

```json
{
  "text": "你好！今天天气很好，阳光明媚。我看到你摸了我的头，是不是想和我一起玩呢？我可以给你讲个有趣的故事哦！",
  "actions": [
    {
      "type": "expression",
      "content": {
        "emotion": "happy",
        "intensity": 0.8,
        "duration": 3000
      }
    },
    {
      "type": "movement",
      "content": {
        "action": "nod",
        "duration": 1000,
        "speed": 0.5
      }
    },
    {
      "type": "light",
      "content": {
        "color": "warm_yellow",
        "brightness": 0.7,
        "pattern": "pulse",
        "duration": 2000
      }
    },
    {
      "type": "sound",
      "content": {
        "sound_type": "chime",
        "volume": 0.6,
        "duration": 500
      }
    }
  ],
  "emotion": "happy",
  "personality_delta": {
    "friendliness": 2,
    "activeness": 1,
    "curiosity": 1
  },
  "context_length": 15,
  "timestamp": 1640995200000,
  "response_time": 1200
}
```

### 2. 心跳消息

#### 发送心跳
- **主题**: `device/heartbeat`
- **QoS**: 0 (尽力而为)
- **方向**: 设备 → 服务器

```json
{
  "device_id": "DEVICE_001",
  "timestamp": 1640995200000,
  "battery_level": 85,
  "wifi_strength": 0.9,
  "status": "online",
  "uptime": 86400,
  "memory_usage": 45.2,
  "cpu_usage": 12.5
}
```

### 3. 状态更新消息

#### 发送状态更新
- **主题**: `device/status`
- **QoS**: 1
- **方向**: 设备 → 服务器

```json
{
  "device_id": "DEVICE_001",
  "status": "online",
  "device_mode": "normal",
  "device_battery": 85,
  "device_volume": 0.7,
  "device_brightness": 0.8,
  "device_network_status": "connected",
  "device_error_codes": [],
  "device_features_enabled": [
    "voice_recognition",
    "emotion_detection",
    "touch_sensing",
    "motion_detection"
  ],
  "firmware_version": "1.2.0",
  "last_update": 1640995200000
}
```

### 4. 传感器数据消息

#### 发送传感器数据
- **主题**: `device/sensor`
- **QoS**: 0
- **方向**: 设备 → 服务器

```json
{
  "device_id": "DEVICE_001",
  "sensor_data": {
    "temperature": 25.5,
    "humidity": 60.2,
    "light_level": 800,
    "noise_level": 45.3,
    "proximity": 0.8,
    "touch_sensors": {
      "head": true,
      "body": false,
      "hands": true,
      "feet": false
    },
    "motion_detected": true,
    "battery_level": 85,
    "wifi_strength": 0.9,
    "accelerometer": {
      "x": 0.1,
      "y": 0.2,
      "z": 9.8
    },
    "gyroscope": {
      "x": 0.0,
      "y": 0.0,
      "z": 0.0
    }
  },
  "timestamp": 1640995200000
}
```

### 5. 错误消息

#### 接收错误信息
- **主题**: `device/{device_id}/server/error`
- **QoS**: 1
- **方向**: 服务器 → 设备

```json
{
  "error": "处理对话失败",
  "message": "设备不存在或已离线",
  "error_code": "DEVICE_NOT_FOUND",
  "timestamp": 1640995200000,
  "request_id": "req_12345",
  "suggestions": [
    "检查设备ID是否正确",
    "确认设备已注册",
    "重新连接MQTT服务器"
  ]
}
```

### 6. 广播消息

#### 接收广播消息
- **主题**: `broadcast/{message_type}`
- **QoS**: 1
- **方向**: 服务器 → 所有设备

```json
{
  "type": "system_maintenance",
  "message": "系统将在10分钟后进行维护",
  "start_time": 1640995200000,
  "duration": 1800000,
  "affected_services": ["chat", "voice"],
  "priority": "high",
  "action_required": false
}
```

---

## 🌐 HTTP管理API

### 基础信息
- **基础URL**: `http://localhost:3000/api/mqtt`
- **认证**: 需要JWT Token (在Authorization头中)
- **响应格式**: JSON
- **字符编码**: UTF-8

### 1. 获取MQTT服务器状态

#### 请求
```http
GET /api/mqtt/status
Authorization: Bearer <jwt_token>
```

#### 响应
```json
{
  "code": 0,
  "message": "获取MQTT状态成功",
  "data": {
    "server_status": "running",
    "connected_devices_count": 5,
    "connected_devices": [
      {
        "clientId": "DEVICE_001",
        "deviceId": "DEVICE_001",
        "connected": true,
        "lastSeen": "2024-01-01T12:00:00.000Z",
        "subscriptions": [
          "device/DEVICE_001/server/chat/response",
          "device/DEVICE_001/server/error",
          "broadcast/#"
        ]
      }
    ],
    "ports": {
      "tcp": 1883,
      "websocket": 8883
    }
  }
}
```

### 2. 获取设备连接状态

#### 请求
```http
GET /api/mqtt/device/{deviceId}/connection
Authorization: Bearer <jwt_token>
```

#### 响应
```json
{
  "code": 0,
  "message": "获取设备连接状态成功",
  "data": {
    "clientId": "DEVICE_001",
    "deviceId": "DEVICE_001",
    "connected": true,
    "lastSeen": "2024-01-01T12:00:00.000Z",
    "subscriptions": [
      "device/DEVICE_001/server/chat/response",
      "device/DEVICE_001/server/error",
      "broadcast/#"
    ]
  }
}
```

### 3. 向设备发送消息

#### 请求
```http
POST /api/mqtt/device/{deviceId}/message
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "topic": "custom/command",
  "payload": {
    "command": "restart",
    "timestamp": 1640995200000,
    "reason": "system_update"
  }
}
```

#### 响应
```json
{
  "code": 0,
  "message": "消息发送成功",
  "data": {
    "device_id": "DEVICE_001",
    "topic": "device/DEVICE_001/custom/command",
    "payload": {
      "command": "restart",
      "timestamp": 1640995200000,
      "reason": "system_update"
    }
  }
}
```

### 4. 广播消息到所有设备

#### 请求
```http
POST /api/mqtt/broadcast
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "topic": "system/update",
  "payload": {
    "type": "firmware_update",
    "version": "1.2.0",
    "url": "https://example.com/firmware.bin",
    "size": 1024000,
    "checksum": "sha256:abc123...",
    "force_update": false
  }
}
```

#### 响应
```json
{
  "code": 0,
  "message": "广播消息发送成功",
  "data": {
    "topic": "broadcast/system/update",
    "payload": {
      "type": "firmware_update",
      "version": "1.2.0",
      "url": "https://example.com/firmware.bin",
      "size": 1024000,
      "checksum": "sha256:abc123...",
      "force_update": false
    }
  }
}
```

### 5. 获取MQTT服务器统计信息

#### 请求
```http
GET /api/mqtt/stats
Authorization: Bearer <jwt_token>
```

#### 响应
```json
{
  "code": 0,
  "message": "获取MQTT统计信息成功",
  "data": {
    "total_connections": 10,
    "online_devices": 8,
    "offline_devices": 2,
    "average_subscriptions": 3.5,
    "server_uptime": 86400,
    "memory_usage": {
      "rss": 52428800,
      "heapTotal": 20971520,
      "heapUsed": 10485760,
      "external": 1048576
    },
    "timestamp": 1640995200000
  }
}
```

### 6. 查询设备连接状态

#### 请求
```http
DELETE /api/mqtt/device/{deviceId}/connection
Authorization: Bearer <jwt_token>
```

#### 响应
```json
{
  "code": 0,
  "message": "设备连接状态查询成功",
  "data": {
    "device_id": "DEVICE_001",
    "connected": true,
    "note": "MQTT协议不支持服务器主动断开连接，设备需要自行断开"
  }
}
```

---

## 🔧 连接配置

### MQTT客户端连接参数

```javascript
const config = {
  host: 'localhost',           // MQTT服务器地址
  port: 1883,                  // TCP端口
  protocol: 'mqtt',            // 协议类型
  username: 'DEVICE_001',      // 设备ID作为用户名
  password: 'your_password',   // 设备密码
  clientId: 'device_001_client_' + Math.random().toString(16).substr(2, 8),
  clean: true,                 // 清理会话
  reconnectPeriod: 1000,       // 重连间隔(毫秒)
  connectTimeout: 30000,       // 连接超时(毫秒)
  keepalive: 60,              // 保活时间(秒)
  qos: 1                      // 默认QoS级别
}
```

### WebSocket连接配置

```javascript
const wsConfig = {
  host: 'localhost',
  port: 8883,
  protocol: 'ws',              // WebSocket协议
  username: 'DEVICE_001',
  password: 'your_password',
  clientId: 'device_001_ws_client',
  clean: true,
  reconnectPeriod: 1000,
  connectTimeout: 30000
}
```

---

## 💻 客户端示例

### JavaScript/Node.js客户端

```javascript
const mqtt = require('mqtt')

// 连接配置
const config = {
  host: 'localhost',
  port: 1883,
  username: 'DEVICE_001',
  password: 'your_password',
  clientId: 'device_001_client',
  clean: true
}

// 连接MQTT服务器
const client = mqtt.connect(config)

// 连接成功
client.on('connect', () => {
  console.log('MQTT连接成功')
  
  // 订阅回复主题
  client.subscribe('device/DEVICE_001/server/chat/response')
  client.subscribe('device/DEVICE_001/server/error')
  client.subscribe('broadcast/#')
})

// 接收消息
client.on('message', (topic, message) => {
  const data = JSON.parse(message.toString())
  
  if (topic.includes('server/chat/response')) {
    console.log('收到AI回复:', data.text)
    // 处理AI回复和动作
    handleAIResponse(data)
  } else if (topic.includes('server/error')) {
    console.error('服务器错误:', data.error)
  } else if (topic.includes('broadcast')) {
    console.log('广播消息:', data)
  }
})

// 发送对话请求
function sendChatRequest(userInput, sensorData) {
  const message = {
    device_id: 'DEVICE_001',
    user_input: userInput,
    sensor_data: sensorData,
    timestamp: Date.now()
  }
  
  client.publish('device/chat', JSON.stringify(message), { qos: 1 })
}

// 发送心跳
function sendHeartbeat() {
  const message = {
    device_id: 'DEVICE_001',
    timestamp: Date.now(),
    battery_level: getBatteryLevel(),
    wifi_strength: getWifiStrength()
  }
  
  client.publish('device/heartbeat', JSON.stringify(message), { qos: 0 })
}

// 定期发送心跳
setInterval(sendHeartbeat, 30000) // 每30秒发送一次心跳
```

### Python客户端

```python
import paho.mqtt.client as mqtt
import json
import time

class MQTTDeviceClient:
    def __init__(self, device_id, server_host, server_port=1883):
        self.device_id = device_id
        self.client = mqtt.Client(client_id=f"{device_id}_client")
        self.client.username_pw_set(device_id, "your_password")
        
        # 设置回调函数
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        
        # 连接到服务器
        self.client.connect(server_host, server_port, 60)
        self.client.loop_start()
    
    def on_connect(self, client, userdata, flags, rc):
        print(f"MQTT连接成功，返回码: {rc}")
        
        # 订阅主题
        client.subscribe(f"device/{self.device_id}/server/chat/response")
        client.subscribe(f"device/{self.device_id}/server/error")
        client.subscribe("broadcast/#")
    
    def on_message(self, client, userdata, msg):
        try:
            data = json.loads(msg.payload.decode())
            
            if "server/chat/response" in msg.topic:
                print(f"收到AI回复: {data['text']}")
                self.handle_ai_response(data)
            elif "server/error" in msg.topic:
                print(f"服务器错误: {data['error']}")
            elif "broadcast" in msg.topic:
                print(f"广播消息: {data}")
        except json.JSONDecodeError:
            print("消息格式错误")
    
    def send_chat_request(self, user_input, sensor_data=None):
        message = {
            "device_id": self.device_id,
            "user_input": user_input,
            "sensor_data": sensor_data or {},
            "timestamp": int(time.time() * 1000)
        }
        
        self.client.publish("device/chat", json.dumps(message), qos=1)
    
    def send_heartbeat(self):
        message = {
            "device_id": self.device_id,
            "timestamp": int(time.time() * 1000),
            "battery_level": self.get_battery_level(),
            "wifi_strength": self.get_wifi_strength()
        }
        
        self.client.publish("device/heartbeat", json.dumps(message), qos=0)
    
    def handle_ai_response(self, data):
        # 处理AI回复和动作
        print(f"AI回复: {data['text']}")
        print(f"情感: {data['emotion']}")
        
        # 执行动作
        for action in data.get('actions', []):
            self.execute_action(action)
    
    def execute_action(self, action):
        action_type = action['type']
        content = action['content']
        
        if action_type == 'expression':
            self.set_expression(content['emotion'], content['intensity'])
        elif action_type == 'movement':
            self.perform_movement(content['action'], content['duration'])
        elif action_type == 'light':
            self.set_light(content['color'], content['brightness'], content['pattern'])
    
    def get_battery_level(self):
        # 获取电池电量
        return 85
    
    def get_wifi_strength(self):
        # 获取WiFi信号强度
        return 0.9
    
    def set_expression(self, emotion, intensity):
        print(f"设置表情: {emotion}, 强度: {intensity}")
    
    def perform_movement(self, action, duration):
        print(f"执行动作: {action}, 持续时间: {duration}ms")
    
    def set_light(self, color, brightness, pattern):
        print(f"设置灯光: 颜色={color}, 亮度={brightness}, 模式={pattern}")

# 使用示例
if __name__ == "__main__":
    client = MQTTDeviceClient("DEVICE_001", "localhost")
    
    # 发送对话请求
    client.send_chat_request("你好，今天天气怎么样？")
    
    # 定期发送心跳
    import threading
    def heartbeat_loop():
        while True:
            client.send_heartbeat()
            time.sleep(30)
    
    heartbeat_thread = threading.Thread(target=heartbeat_loop, daemon=True)
    heartbeat_thread.start()
    
    # 保持程序运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("程序退出")
```

---

## 🧪 测试方法

### 1. 启动测试客户端

```bash
# 进入backend目录
cd backend

# 启动测试客户端
node test-mqtt-client.js
```

### 2. 测试内容

测试脚本会自动执行以下测试：

- ✅ **连接认证**: 验证设备认证机制
- ✅ **心跳消息**: 测试心跳功能
- ✅ **状态更新**: 测试设备状态同步
- ✅ **传感器数据**: 测试传感器数据上传
- ✅ **基础对话**: 测试简单对话功能
- ✅ **增强对话**: 测试完整数据对话
- ✅ **多轮对话**: 测试连续对话
- ✅ **错误处理**: 测试错误情况处理

### 3. 手动测试

#### 使用curl测试HTTP API

```bash
# 获取MQTT状态
curl -H "Authorization: Bearer <your_token>" \
     http://localhost:3000/api/mqtt/status

# 发送广播消息
curl -X POST \
     -H "Authorization: Bearer <your_token>" \
     -H "Content-Type: application/json" \
     -d '{"topic":"test","payload":{"message":"Hello World"}}' \
     http://localhost:3000/api/mqtt/broadcast
```

#### 使用MQTT客户端工具

```bash
# 使用mosquitto客户端测试
mosquitto_pub -h localhost -p 1883 -u DEVICE_001 -P your_password \
  -t "device/chat" \
  -m '{"device_id":"DEVICE_001","user_input":"测试消息"}'
```

---

## ❌ 错误码说明

### HTTP API错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 401 | 认证失败 | 检查JWT Token是否有效 |
| 404 | 资源不存在 | 检查设备ID是否正确 |
| 500 | 服务器内部错误 | 查看服务器日志 |

### MQTT错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| CONNECTION_REFUSED | 连接被拒绝 | 检查服务器地址和端口 |
| AUTHENTICATION_FAILED | 认证失败 | 检查用户名和密码 |
| DEVICE_NOT_FOUND | 设备不存在 | 确认设备已注册 |
| MESSAGE_TOO_LARGE | 消息过大 | 减少消息内容大小 |

### 常见问题解决

#### 1. 连接失败
```bash
# 检查端口是否开放
netstat -an | grep 1883

# 检查防火墙
sudo ufw status
```

#### 2. 认证失败
```bash
# 检查设备是否存在于数据库
mysql -u aidoll -p aidoll -e "SELECT * FROM devices WHERE device_id='DEVICE_001'"
```

#### 3. 消息丢失
- 检查QoS设置
- 确认网络稳定性
- 查看服务器日志

---

## 📊 性能指标

### 推荐配置

- **最大连接数**: 10000
- **消息吞吐量**: 10000 msg/s
- **响应时间**: < 100ms
- **内存使用**: < 1GB
- **CPU使用**: < 50%

### 监控指标

- 连接设备数量
- 消息吞吐量
- 响应时间
- 错误率
- 内存使用

---

## 🔒 安全考虑

### 传输安全
- 使用TLS加密传输
- 配置SSL证书
- 限制连接来源IP

### 认证安全
- 强密码策略
- 定期更新密码
- 设备白名单机制

### 数据安全
- 消息加密
- 访问控制
- 审计日志

---

## 📝 版本历史

- **v1.0.0**: 初始MQTT支持
- **v1.1.0**: 添加WebSocket支持
- **v1.2.0**: 增强认证机制
- **v1.3.0**: 添加广播功能
- **v1.4.0**: 性能优化和监控
- **v1.5.0**: 完整API文档

---

## 📞 技术支持

如有问题，请联系技术支持团队：

- **邮箱**: <EMAIL>
- **电话**: 400-123-4567
- **在线文档**: https://docs.aidoll.com/mqtt

---

*最后更新时间: 2024年1月1日* 
