# 厂商适配API文档

## 概述

厂商适配API是专门为硬件厂商设计的接口，用于对接玩偶硬件设备。通过这些接口，厂商可以：

1. **发送对话请求** - 将用户与玩偶的对话抄送给AI系统
2. **接收控制指令** - 获取玩偶的屏幕显示和舵机动作指令
3. **更新设备状态** - 报告设备在线状态和健康信息

## 接口列表

### 1. 厂商对话回调

**接口地址**: `POST /api/vendor/callback`

**功能描述**: 接收厂商抄送的用户对话，返回AI回复和硬件控制指令

**请求参数**:
```json
{
  "device_id": "DOLL_001",              // 设备ID（必填）
  "user_input": "你好，今天心情不好",    // 用户输入（必填）
  "timestamp": 1640995200000,           // 时间戳（可选）
  "vendor_data": {                      // 厂商特有数据（可选）
    "battery_level": 85,
    "wifi_strength": 0.9
  }
}
```

**响应格式**:
```json
{
  "device_id": "DOLL_001",
  "response_text": "哦，怎么了？想和我聊聊吗？",
  "screen": {
    "image_url": "https://assets.example.com/emotions/sad.gif",
    "image_timer": 100,      // 刷新间隔(毫秒)
    "image_counter": 30,     // 播放次数
    "image_mode": "once"     // 播放模式: "once" | "loop"
  },
  "servos": {
    "left_ear": 0,           // 左耳舵机: -1, 0, 1
    "right_ear": 0,          // 右耳舵机: -1, 0, 1  
    "tail": -1               // 尾巴舵机: -1, 0, 1
  },
  "timestamp": 1640995200000
}
```

### 2. 设备心跳上报

**接口地址**: `POST /api/vendor/heartbeat`

**功能描述**: 更新设备在线状态和健康信息

**请求参数**:
```json
{
  "device_id": "DOLL_001",      // 设备ID（必填）
  "status": "online",           // 设备状态（可选）
  "battery_level": 90,          // 电池电量（可选）
  "wifi_strength": 0.95         // WiFi信号强度（可选）
}
```

**响应格式**:
```json
{
  "code": 0,
  "message": "心跳更新成功",
  "timestamp": 1640995200000
}
```

### 3. 设备状态查询

**接口地址**: `GET /api/vendor/device/{device_id}/status`

**功能描述**: 查询指定设备的状态信息

**响应格式**:
```json
{
  "code": 0,
  "message": "获取设备状态成功",
  "data": {
    "device_id": "DOLL_001",
    "name": "小白",
    "status": "online",
    "last_heartbeat": "2024-01-01T12:00:00.000Z",
    "character_type": "companion",
    "online": true
  }
}
```

## 硬件控制协议

### 屏幕控制

玩偶的眼睛屏幕用于显示表情，控制参数如下：

```json
{
  "image_url": "https://assets.example.com/emotions/smile.gif",
  "image_timer": 100,     // 每帧间隔时间（毫秒）
  "image_counter": 30,    // 总帧数/播放次数
  "image_mode": "once"    // once: 播放一次, loop: 循环播放
}
```

**内置表情资源**:
- `smile.gif` - 微笑
- `happy.gif` - 开心
- `sad.gif` - 悲伤
- `angry.gif` - 愤怒
- `surprised.gif` - 惊讶
- `neutral.gif` - 中性
- `blink.gif` - 眨眼
- `sleepy.gif` - 困倦

### 舵机控制

玩偶有三个舵机分别控制左耳、右耳和尾巴：

```json
{
  "left_ear": 1,    // 左耳: -1(向下), 0(中性), 1(向上)
  "right_ear": 1,   // 右耳: -1(向下), 0(中性), 1(向上)
  "tail": 1         // 尾巴: -1(向左), 0(中性), 1(向右)
}
```

**常用组合动作**:
- **挥手**: `{left_ear: 1, right_ear: 1, tail: 1}`
- **点头**: `{left_ear: 0, right_ear: 0, tail: 1}`
- **摇头**: `{left_ear: -1, right_ear: 1, tail: 0}`
- **安慰**: `{left_ear: 0, right_ear: 0, tail: -1}`
- **警觉**: `{left_ear: 1, right_ear: 1, tail: 0}`

## 情绪与动作映射

系统会根据用户输入的情绪自动生成相应的表情和动作：

| 用户情绪 | 表情 | 动作 | 说明 |
|---------|------|------|------|
| 开心 | smile | cheer | 用户表达积极情绪时 |
| 悲伤 | sad | comfort | 用户表达负面情绪时 |
| 惊讶 | surprised | alert | 用户表达惊讶时 |
| 中性 | neutral | nod | 普通对话时 |

## 使用示例

### 完整对话流程

```javascript
// 1. 用户对玩偶说话
// 2. 厂商将对话抄送给AI系统
const response = await fetch('/api/vendor/callback', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    device_id: 'DOLL_001',
    user_input: '我今天考试考得很好！'
  })
});

const data = await response.json();
// 3. 厂商接收控制指令并执行
// {
//   "response_text": "太棒了！我也为你感到高兴！",
//   "screen": {
//     "image_url": ".../smile.gif",
//     "image_timer": 100,
//     "image_counter": 30,
//     "image_mode": "once"
//   },
//   "servos": {
//     "left_ear": 1,
//     "right_ear": 1,
//     "tail": 1
//   }
// }

// 4. 玩偶执行动作
// - 播放语音: "太棒了！我也为你感到高兴！"
// - 显示表情: 播放微笑动画30帧
// - 执行动作: 两耳竖起，尾巴摆动（欢呼动作）
```

### 心跳上报示例

```javascript
// 定期发送心跳（建议每30秒一次）
setInterval(async () => {
  await fetch('/api/vendor/heartbeat', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      device_id: 'DOLL_001',
      status: 'online',
      battery_level: getBatteryLevel(),
      wifi_strength: getWifiStrength()
    })
  });
}, 30000);
```

## 错误处理

所有接口在出错时返回统一的错误格式：

```json
{
  "code": 400,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

**常见错误码**:
- `400` - 请求参数错误
- `404` - 设备不存在
- `429` - 请求过于频繁
- `500` - 服务器内部错误

## 测试工具

使用提供的测试脚本验证功能：

```bash
# 运行厂商适配测试
node test-vendor-adapter.js
```

测试脚本会验证：
- ✅ 对话回调功能
- ✅ 设备状态查询
- ✅ 心跳上报功能
- ✅ 动作指令生成

## 集成指南

### 第一步：设备注册
在管理后台添加设备，获取 `device_id`

### 第二步：实现回调
在厂商系统中集成对话回调接口

### 第三步：动作执行
根据返回的控制指令执行屏幕显示和舵机动作

### 第四步：心跳维护
定期发送设备心跳保持在线状态

---

**技术支持**: 如有问题请联系开发团队
**文档版本**: v1.0
**更新时间**: 2024-01-01 
