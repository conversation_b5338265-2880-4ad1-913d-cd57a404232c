#!/bin/bash

# MQTT连接问题快速修复脚本

echo "🔧 MQTT连接问题快速修复脚本"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. 检查当前目录
if [ ! -f "package.json" ]; then
    print_error "请在backend目录下运行此脚本"
    exit 1
fi

print_info "当前目录: $(pwd)"

# 2. 停止可能运行的MQTT客户端进程
print_info "停止可能冲突的MQTT进程..."
pkill -f "vendor.*mqtt" 2>/dev/null || true
pkill -f "mqtt.*client" 2>/dev/null || true
print_success "已停止旧的MQTT进程"

# 3. 检查.env文件
print_info "检查.env文件配置..."
if [ ! -f ".env" ]; then
    print_warning ".env文件不存在，从.env.example复制"
    cp .env.example .env
fi

# 检查是否有MQTT_XSTAR配置
if ! grep -q "MQTT_XSTAR_HOST" .env; then
    print_warning "添加MQTT_XSTAR配置到.env文件"
    cat >> .env << EOF

# 外部MQTT配置（连接到厂商MQTT服务器）
MQTT_XSTAR_HOST=**************
MQTT_XSTAR_PORT=10079
MQTT_XSTAR_USERNAME=xstar_platform
MQTT_XSTAR_PASSWORD=your_mqtt_api_key
MQTT_XSTAR_CLIENT_ID=platform_client
EOF
fi

print_success ".env文件配置检查完成"

# 4. 备份可能冲突的旧文件
print_info "备份可能冲突的旧MQTT客户端文件..."

if [ -f "src/services/vendorMqttClientService.js" ]; then
    mv src/services/vendorMqttClientService.js src/services/vendorMqttClientService.js.backup
    print_success "已备份 vendorMqttClientService.js"
fi

if [ -f "src/routes/vendorMqttRoutes.js" ]; then
    mv src/routes/vendorMqttRoutes.js src/routes/vendorMqttRoutes.js.backup
    print_success "已备份 vendorMqttRoutes.js"
fi

# 5. 更新app.ts，移除旧的路由引用
print_info "更新app.ts，移除旧的MQTT路由引用..."
if grep -q "vendorMqttRoutes" src/app.ts; then
    sed -i.backup 's/const vendorMqttRoutes = require.*vendorMqttRoutes.*/\/\/ const vendorMqttRoutes = require(".\/routes\/vendorMqttRoutes") \/\/ 已禁用/' src/app.ts
    sed -i 's/app\.use.*vendor-mqtt.*vendorMqttRoutes.*/\/\/ app.use("\/api\/vendor-mqtt", vendorMqttRoutes) \/\/ 已禁用/' src/app.ts
    print_success "已更新app.ts，禁用旧的MQTT路由"
fi

# 6. 检查TypeScript编译
print_info "检查TypeScript编译..."
if command -v tsc &> /dev/null; then
    npm run build
    print_success "TypeScript编译完成"
else
    print_warning "TypeScript编译器未找到，跳过编译"
fi

# 7. 显示配置信息
print_info "当前MQTT配置:"
echo "================================"
grep "MQTT_XSTAR" .env || print_warning "未找到MQTT_XSTAR配置"
echo "================================"

# 8. 提供下一步指导
echo ""
print_success "修复完成！"
echo ""
print_info "下一步操作:"
echo "1. 请在.env文件中设置正确的MQTT_XSTAR_PASSWORD"
echo "2. 运行 'npm run dev' 启动服务"
echo "3. 查看日志确认MQTT连接成功"
echo ""
print_info "预期看到的成功日志:"
echo "   外部MQTT连接成功"
echo "   ✅ 成功订阅主题: xstar/callback"
echo "   ✅ 成功订阅主题: xstar/heartbeat"
echo "   📱 设备 DOLL_001 已完成主题订阅"
echo ""
print_warning "如果仍有问题，请查看 mqtt-connection-fix.md 文档"
