version: '3.8'

services:
  # AI对话平台主服务
  ai-platform:
    build: .
    container_name: ai-platform
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOST=0.0.0.0
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=aidoll
      - DB_PASSWORD=aidoll_password_2024
      - DB_NAME=aidoll
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password_2024
      - JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
      - MQTT_XSTAR_HOST=**************
      - MQTT_XSTAR_PORT=10079
      - MQTT_XSTAR_USERNAME=xstar_platform
      - MQTT_XSTAR_PASSWORD=your_mqtt_api_key
      - ALIYUN_API_KEY=your_aliyun_dashscope_api_key
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/app/logs
    networks:
      - ai-platform-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: ai-platform-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=root_password_2024
      - MYSQL_DATABASE=aidoll
      - MYSQL_USER=aidoll
      - MYSQL_PASSWORD=aidoll_password_2024
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./src/config/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - ai-platform-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ai-platform-redis
    restart: unless-stopped
    command: redis-server --requirepass redis_password_2024 --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-platform-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: ai-platform-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - ai-platform
    networks:
      - ai-platform-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  ai-platform-network:
    driver: bridge
