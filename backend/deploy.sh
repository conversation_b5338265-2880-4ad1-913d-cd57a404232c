#!/bin/bash

# 多用户AI对话平台部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev|prod
# 操作: build|start|stop|restart|logs|clean

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
}

# 检查环境文件
check_env() {
    if [ ! -f .env ]; then
        print_warning ".env文件不存在，从.env.example复制"
        cp .env.example .env
        print_warning "请编辑.env文件配置正确的参数"
    fi
}

# 构建镜像
build_image() {
    print_message "开始构建Docker镜像..."
    docker-compose build --no-cache
    print_message "Docker镜像构建完成"
}

# 启动服务
start_services() {
    print_message "启动服务..."
    docker-compose up -d
    
    print_message "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        print_message "服务启动成功！"
        print_message "访问地址: http://localhost:3000"
        print_message "API文档: http://localhost:3000/api"
        
        # 显示服务状态
        docker-compose ps
    else
        print_error "服务启动失败，请检查日志"
        docker-compose logs
        exit 1
    fi
}

# 停止服务
stop_services() {
    print_message "停止服务..."
    docker-compose down
    print_message "服务已停止"
}

# 重启服务
restart_services() {
    print_message "重启服务..."
    docker-compose restart
    print_message "服务已重启"
}

# 查看日志
show_logs() {
    local service=${1:-""}
    if [ -n "$service" ]; then
        docker-compose logs -f "$service"
    else
        docker-compose logs -f
    fi
}

# 清理资源
clean_resources() {
    print_warning "这将删除所有容器、镜像和数据卷，确定要继续吗？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_message "清理Docker资源..."
        docker-compose down -v --rmi all
        docker system prune -f
        print_message "清理完成"
    else
        print_message "取消清理操作"
    fi
}

# 运行系统检查
run_system_check() {
    print_message "运行系统检查..."
    if [ -f system-check.js ]; then
        node system-check.js
    else
        print_warning "系统检查脚本不存在"
    fi
}

# 数据库初始化
init_database() {
    print_message "初始化数据库..."
    docker-compose exec mysql mysql -u root -proot_password_2024 aidoll < /docker-entrypoint-initdb.d/init.sql
    print_message "数据库初始化完成"
}

# 备份数据
backup_data() {
    local backup_dir="backups/$(date +'%Y%m%d_%H%M%S')"
    mkdir -p "$backup_dir"
    
    print_message "备份数据到 $backup_dir..."
    
    # 备份数据库
    docker-compose exec mysql mysqldump -u root -proot_password_2024 aidoll > "$backup_dir/database.sql"
    
    # 备份Redis数据
    docker-compose exec redis redis-cli --rdb - > "$backup_dir/redis.rdb"
    
    # 备份日志
    cp -r logs "$backup_dir/" 2>/dev/null || true
    
    print_message "数据备份完成: $backup_dir"
}

# 显示帮助信息
show_help() {
    echo "多用户AI对话平台部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [操作]"
    echo ""
    echo "操作:"
    echo "  build     构建Docker镜像"
    echo "  start     启动所有服务"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  logs      查看日志 (可指定服务名)"
    echo "  clean     清理所有Docker资源"
    echo "  check     运行系统检查"
    echo "  init-db   初始化数据库"
    echo "  backup    备份数据"
    echo "  help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build"
    echo "  $0 start"
    echo "  $0 logs ai-platform"
    echo "  $0 backup"
}

# 主函数
main() {
    local action=${1:-"help"}
    
    print_message "多用户AI对话平台部署脚本"
    print_message "操作: $action"
    
    # 检查Docker
    check_docker
    
    # 检查环境文件
    check_env
    
    case $action in
        "build")
            build_image
            ;;
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs "$2"
            ;;
        "clean")
            clean_resources
            ;;
        "check")
            run_system_check
            ;;
        "init-db")
            init_database
            ;;
        "backup")
            backup_data
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"
