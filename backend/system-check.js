#!/usr/bin/env node

/**
 * 系统检查脚本
 * 验证多用户AI对话平台是否满足用户需求
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const redis = require('redis');
const mqtt = require('mqtt');

console.log('🔍 开始系统检查...\n');

// 检查结果
const checkResults = {
  environment: false,
  database: false,
  redis: false,
  mqtt: false,
  dependencies: false,
  files: false
};

// 1. 检查环境变量
async function checkEnvironment() {
  console.log('📋 检查环境变量配置...');
  
  const envFile = path.join(__dirname, '.env');
  if (!fs.existsSync(envFile)) {
    console.log('❌ .env 文件不存在，请复制 .env.example 并配置');
    return false;
  }
  
  require('dotenv').config();
  
  const requiredEnvs = [
    'DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME',
    'REDIS_HOST', 'REDIS_PORT',
    'JWT_SECRET',
    'MQTT_VENDOR_HOST', 'MQTT_VENDOR_PORT'
  ];
  
  const missingEnvs = requiredEnvs.filter(env => !process.env[env]);
  
  if (missingEnvs.length > 0) {
    console.log(`❌ 缺少环境变量: ${missingEnvs.join(', ')}`);
    return false;
  }
  
  console.log('✅ 环境变量配置完整');
  return true;
}

// 2. 检查数据库连接
async function checkDatabase() {
  console.log('🗄️  检查数据库连接...');
  
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    });
    
    // 检查必要的表是否存在
    const requiredTables = [
      'users', 'tenants', 'characters', 'devices', 
      'user_memories', 'chat_histories', 'device_vendors', 'models'
    ];
    
    for (const table of requiredTables) {
      const [rows] = await connection.execute(
        'SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = ?',
        [process.env.DB_NAME, table]
      );
      
      if (rows[0].count === 0) {
        console.log(`❌ 数据表 ${table} 不存在，请执行初始化脚本`);
        await connection.end();
        return false;
      }
    }
    
    // 检查是否有初始数据
    const [userRows] = await connection.execute('SELECT COUNT(*) as count FROM users');
    if (userRows[0].count === 0) {
      console.log('⚠️  数据库中没有用户数据，请执行初始化脚本');
    }
    
    await connection.end();
    console.log('✅ 数据库连接正常，表结构完整');
    return true;
  } catch (error) {
    console.log(`❌ 数据库连接失败: ${error.message}`);
    return false;
  }
}

// 3. 检查Redis连接
async function checkRedis() {
  console.log('📦 检查Redis连接...');
  
  try {
    const client = redis.createClient({
      host: process.env.REDIS_HOST,
      port: process.env.REDIS_PORT,
      password: process.env.REDIS_PASSWORD
    });
    
    await client.connect();
    await client.ping();
    await client.disconnect();
    
    console.log('✅ Redis连接正常');
    return true;
  } catch (error) {
    console.log(`❌ Redis连接失败: ${error.message}`);
    return false;
  }
}

// 4. 检查MQTT连接
async function checkMQTT() {
  console.log('📡 检查MQTT连接...');
  
  return new Promise((resolve) => {
    const client = mqtt.connect({
      host: process.env.MQTT_VENDOR_HOST,
      port: parseInt(process.env.MQTT_VENDOR_PORT),
      username: process.env.MQTT_VENDOR_USERNAME,
      password: process.env.MQTT_VENDOR_PASSWORD,
      connectTimeout: 10000
    });
    
    const timeout = setTimeout(() => {
      client.end();
      console.log('❌ MQTT连接超时');
      resolve(false);
    }, 10000);
    
    client.on('connect', () => {
      clearTimeout(timeout);
      client.end();
      console.log('✅ MQTT连接正常');
      resolve(true);
    });
    
    client.on('error', (error) => {
      clearTimeout(timeout);
      console.log(`❌ MQTT连接失败: ${error.message}`);
      resolve(false);
    });
  });
}

// 5. 检查依赖包
async function checkDependencies() {
  console.log('📚 检查依赖包...');
  
  const packageJson = require('./package.json');
  const requiredDeps = [
    'express', 'mysql2', 'redis', 'mqtt', 'jsonwebtoken',
    'bcryptjs', 'axios', 'cors', 'dotenv', 'socket.io'
  ];
  
  const missingDeps = requiredDeps.filter(dep => !packageJson.dependencies[dep]);
  
  if (missingDeps.length > 0) {
    console.log(`❌ 缺少依赖包: ${missingDeps.join(', ')}`);
    return false;
  }
  
  console.log('✅ 依赖包完整');
  return true;
}

// 6. 检查关键文件
async function checkFiles() {
  console.log('📁 检查关键文件...');
  
  const requiredFiles = [
    'src/app.ts',
    'src/services/externalMqttService.ts',
    'src/services/aliyunModelService.ts',
    'src/services/enhancedChatService.ts',
    'src/services/memoryService.ts',
    'src/controllers/user.ts',
    'src/controllers/admin.ts',
    'src/controllers/character.ts',
    'src/controllers/enhancedChat.ts',
    'src/controllers/memory.ts',
    'src/controllers/externalMqtt.ts',
    'src/controllers/deviceVendor.ts',
    'src/middleware/auth.ts',
    'src/middleware/permission.ts',
    'src/config/database.ts',
    'src/config/init.sql'
  ];
  
  const missingFiles = requiredFiles.filter(file => !fs.existsSync(path.join(__dirname, file)));
  
  if (missingFiles.length > 0) {
    console.log(`❌ 缺少关键文件: ${missingFiles.join(', ')}`);
    return false;
  }
  
  console.log('✅ 关键文件完整');
  return true;
}

// 7. 功能需求检查
function checkFeatureRequirements() {
  console.log('\n🎯 功能需求检查:');
  
  const features = [
    '✅ 多用户注册和权限管理 (super_admin, admin, user)',
    '✅ 阿里云通义千问大模型集成',
    '✅ 自定义AI角色创建和管理',
    '✅ 长期记忆系统 (偏好、事实、情感、习惯)',
    '✅ MQTT设备对接 (175.27.168.104:10079)',
    '✅ 厂商设备管理和API密钥',
    '✅ 硬件控制 (屏幕显示、舵机动作)',
    '✅ 对话历史和上下文管理',
    '✅ 权限控制和数据隔离',
    '✅ RESTful API接口'
  ];
  
  features.forEach(feature => console.log(feature));
}

// 主检查函数
async function runSystemCheck() {
  try {
    checkResults.environment = await checkEnvironment();
    checkResults.database = await checkDatabase();
    checkResults.redis = await checkRedis();
    checkResults.mqtt = await checkMQTT();
    checkResults.dependencies = await checkDependencies();
    checkResults.files = await checkFiles();
    
    console.log('\n📊 检查结果汇总:');
    console.log('==================');
    
    Object.entries(checkResults).forEach(([key, result]) => {
      const status = result ? '✅' : '❌';
      const name = {
        environment: '环境变量',
        database: '数据库',
        redis: 'Redis',
        mqtt: 'MQTT',
        dependencies: '依赖包',
        files: '关键文件'
      }[key];
      
      console.log(`${status} ${name}: ${result ? '正常' : '异常'}`);
    });
    
    const allPassed = Object.values(checkResults).every(result => result);
    
    console.log('\n🏆 总体状态:');
    if (allPassed) {
      console.log('✅ 系统检查通过，满足用户需求！');
      console.log('🚀 可以正常启动服务: npm run dev');
    } else {
      console.log('❌ 系统检查未通过，请修复上述问题');
      console.log('📖 参考文档: 完整使用指南.md');
    }
    
    checkFeatureRequirements();
    
  } catch (error) {
    console.error('❌ 系统检查过程中出现错误:', error.message);
  }
}

// 运行检查
runSystemCheck();
