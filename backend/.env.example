# 服务器配置
PORT=3000
HOST=0.0.0.0

# JWT密钥
JWT_SECRET=your_super_secret_jwt_key_here

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=aidoll

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 阿里云配置
ALIYUN_API_KEY=your_aliyun_dashscope_api_key

# 外部MQTT配置（连接到厂商MQTT服务器）
MQTT_XSTAR_HOST=**************
MQTT_XSTAR_PORT=10079
MQTT_XSTAR_USERNAME=xstar_platform
MQTT_XSTAR_PASSWORD=your_mqtt_api_key
MQTT_XSTAR_CLIENT_ID=platform_client

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs
