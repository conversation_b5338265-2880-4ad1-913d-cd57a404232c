# MQTT日志系统说明

## 🎯 功能概述

已成功实现完整的MQTT消息日志系统，所有接收到的和发送的数据都会在MQTT连接日志中详细展示。

## ✅ 已实现的功能

### 1. 详细日志记录

#### 控制台实时日志 ✅
**接收消息日志格式：**
```
📥 [MQTT收到] [设备:DOLL_001] [厂商:ABC_TOYS]
  主题: xstar/callback
  大小: 156 bytes
  时间: 2024-01-01T12:00:00.000Z
  内容: {"device_id":"DOLL_001","user_input":"你好","xstar_id":"ABC_TOYS","timestamp":1640995200000}
────────────────────────────────────────────────────────────────────────────────
```

**发送消息日志格式：**
```
📤 [MQTT发送] [设备:DOLL_001]
  主题: xstar/DOLL_001/response
  大小: 324 bytes
  时间: 2024-01-01T12:00:01.000Z
  内容: {"device_id":"DOLL_001","response_text":"你好！很高兴见到你","screen":{"image_url":"..."},"servos":{"left_ear":[1,0,1,0]},"timestamp":1640995201000}
────────────────────────────────────────────────────────────────────────────────
```

**AI处理日志：**
```
🤖 [AI回复生成] 设备:DOLL_001
  用户输入: 你好
  AI回复: 你好！很高兴见到你
  情感: happy
  硬件控制: {"screen":{"image_url":"https://assets.ai-doll.cn/emotions/happy.gif"},"servos":{"left_ear":[1,0,1,0],"right_ear":[1,0,1,0],"tail":[1,1,1,1]}}
```

**心跳处理日志：**
```
💓 [心跳处理] 设备:DOLL_001
  电池电量: 85%
  WiFi信号: 92.0%
  时间戳: 2024-01-01T12:00:00.000Z
```

#### 数据库持久化日志 ✅
**表结构：**
```sql
CREATE TABLE mqtt_logs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  direction ENUM('received', 'sent') NOT NULL,
  topic VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  message_size INT NOT NULL DEFAULT 0,
  device_id VARCHAR(50),
  xstar_id VARCHAR(50),
  success BOOLEAN NOT NULL DEFAULT TRUE,
  error_message TEXT,
  timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### 2. API接口

#### 获取设备MQTT日志 ✅
```bash
GET /api/external-mqtt/devices/{device_id}/logs?page=1&limit=20&type=received

# 响应示例
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 123,
        "device_id": "DOLL_001",
        "xstar_id": "ABC_TOYS",
        "direction": "received",
        "topic": "xstar/callback",
        "message": "{\"device_id\":\"DOLL_001\",\"user_input\":\"你好\"}",
        "message_preview": "用户输入: 你好",
        "message_type": "callback",
        "message_size": 156,
        "success": true,
        "error_message": null,
        "timestamp": "2024-01-01T12:00:00.000Z"
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 20
  }
}
```

#### 获取MQTT消息统计 ✅
```bash
GET /api/external-mqtt/message-stats?device_id=DOLL_001&hours=24

# 响应示例
{
  "code": 0,
  "data": {
    "summary": {
      "received": {
        "success": 45,
        "failed": 2,
        "total_size": 15680
      },
      "sent": {
        "success": 43,
        "failed": 0,
        "total_size": 28934
      }
    },
    "topic_stats": [
      {
        "topic": "xstar/callback",
        "direction": "received",
        "count": 25,
        "total_size": 8900
      },
      {
        "topic": "xstar/DOLL_001/response",
        "direction": "sent",
        "count": 25,
        "total_size": 18500
      }
    ],
    "time_distribution": [
      {
        "hour": "2024-01-01 12:00:00",
        "direction": "received",
        "count": 5
      }
    ]
  }
}
```

### 3. 消息类型识别

#### 自动消息分类 ✅
- **callback**: 用户对话输入
- **response**: AI回复消息
- **heartbeat**: 设备心跳
- **heartbeat_ack**: 心跳确认
- **error**: 错误消息

#### 智能消息预览 ✅
```javascript
// 对话消息
"用户输入: 你好，今天天气怎么样？"
"AI回复: 今天天气很好，阳光明媚！"

// 心跳消息
"心跳: 电量85%, WiFi92.0%"
"心跳确认: 2024-01-01T12:00:00.000Z"

// 错误消息
"错误: 设备不存在"
```

### 4. 权限控制

#### 数据访问权限 ✅
- **super_admin**: 可查看所有设备的MQTT日志
- **admin**: 可查看本租户设备的MQTT日志
- **user**: 只能查看自己设备的MQTT日志

#### 设备权限验证 ✅
```javascript
// 验证用户是否有权限访问设备日志
const canAccess = device.user_id === currentUser.id ||
                 (currentUser.role === 'admin' && device.tenant_id === currentUser.tenant_id) ||
                 currentUser.role === 'super_admin'
```

## 🔧 技术实现

### 1. 日志记录流程

```mermaid
sequenceDiagram
    participant Device as 设备
    participant MQTT as MQTT服务器
    participant Service as MQTT服务
    participant DB as 数据库
    participant Console as 控制台

    Device->>MQTT: 发送消息
    MQTT->>Service: 转发消息
    Service->>Console: 输出详细日志
    Service->>DB: 存储日志记录
    Service->>Service: 处理业务逻辑
    Service->>MQTT: 发送回复
    Service->>Console: 输出发送日志
    Service->>DB: 存储发送记录
    MQTT->>Device: 转发回复
```

### 2. 错误处理

#### 消息解析错误 ✅
```javascript
try {
  parsedMessage = JSON.parse(messageStr)
  await this.logMqttMessage('received', topic, parsedMessage, deviceId, xstarId)
} catch (error) {
  await this.logMqttError('received', topic, messageStr, error, deviceId, xstarId)
}
```

#### 发送失败处理 ✅
```javascript
this.client.publish(topic, payload, { qos: 1 }, async (error) => {
  if (error) {
    await this.logMqttError('sent', topic, message, error, deviceId)
  } else {
    await this.logMqttMessage('sent', topic, message, deviceId)
  }
})
```

### 3. 性能优化

#### 异步日志记录 ✅
- 日志记录不阻塞主要业务流程
- 使用异步数据库操作
- 错误处理不影响消息处理

#### 数据库索引 ✅
```sql
KEY `idx_device_id` (`device_id`),
KEY `idx_xstar_id` (`xstar_id`),
KEY `idx_direction` (`direction`),
KEY `idx_topic` (`topic`),
KEY `idx_timestamp` (`timestamp`),
KEY `idx_success` (`success`)
```

## 📊 监控和分析

### 1. 实时监控

#### 控制台日志监控 ✅
- 实时显示所有MQTT消息
- 彩色标识不同类型的消息
- 详细的消息内容和元数据

#### 错误日志监控 ✅
```
❌ [MQTT接收错误] [设备:DOLL_001] [厂商:ABC_TOYS]
  主题: xstar/callback
  错误: Unexpected token in JSON
  内容: {"device_id":"DOLL_001","user_input":
────────────────────────────────────────────────────────────────────────────────
```

### 2. 统计分析

#### 消息量统计 ✅
- 按时间段统计消息数量
- 按设备统计消息分布
- 按主题统计消息类型

#### 成功率分析 ✅
- 消息发送成功率
- 消息接收成功率
- 错误类型分析

#### 性能分析 ✅
- 消息大小统计
- 处理时间分析
- 流量分布统计

## 🔍 使用示例

### 1. 查看设备日志
```bash
# 查看DOLL_001设备的所有日志
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:3000/api/external-mqtt/devices/DOLL_001/logs?limit=50"

# 只查看接收到的消息
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:3000/api/external-mqtt/devices/DOLL_001/logs?type=received"

# 只查看发送的消息
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:3000/api/external-mqtt/devices/DOLL_001/logs?type=sent"
```

### 2. 查看统计信息
```bash
# 查看过去24小时的消息统计
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:3000/api/external-mqtt/message-stats?hours=24"

# 查看特定设备的统计
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:3000/api/external-mqtt/message-stats?device_id=DOLL_001&hours=12"
```

### 3. 实时监控
```bash
# 启动服务后，控制台会实时显示所有MQTT消息
npm run dev

# 或使用Docker
./deploy.sh logs -f ai-platform
```

## 🎉 总结

MQTT日志系统已完全实现：

- ✅ **完整日志记录** - 所有收发消息都有详细记录
- ✅ **实时控制台输出** - 彩色格式化的实时日志
- ✅ **数据库持久化** - 完整的历史记录存储
- ✅ **智能消息分类** - 自动识别消息类型
- ✅ **权限控制** - 基于用户角色的访问控制
- ✅ **统计分析** - 丰富的统计和分析功能
- ✅ **错误处理** - 完善的错误记录和处理
- ✅ **性能优化** - 异步处理和数据库索引

现在您可以通过控制台和API接口查看所有MQTT消息的详细信息，包括消息内容、大小、时间戳、成功状态等！
